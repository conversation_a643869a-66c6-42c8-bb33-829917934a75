export const CALLBACKS = {
  // Main Menu
  MAIN_MENU: "main_menu",
  TASKS_MENU: "tasks_menu",
  BIRTHDAYS_MENU: "birthdays_menu",
  EVENTS_MENU: "events_menu",
  NOTIFICATIONS_MENU: "notifications_menu",
  <PERSON>ARCH_MENU: "search_menu",
  CALENDAR_MENU: "calendar_menu",
  SETTINGS_MENU: "settings_menu",
  HELP_MENU: "help_menu",

  // Task Actions
  TASK_CREATE: "task_create",
  TASK_LIST: "task_list",
  TASK_PENDING: "task_pending",
  TASK_COMPLETED: "task_completed",
  TASK_SEARCH: "task_search",

  // Birthday Actions
  BIRTHDAY_ADD: "birthday_add",
  BIRTHDAY_LIST: "birthday_list",
  BIRTHDAY_UPCOMING: "birthday_upcoming",
  BIRTHDAY_SEARCH: "birthday_search",

  // Event Actions
  EVENT_CREATE: "event_create",
  EVENTS_LIST: "events_list",
  EVENTS_UPCOMING: "events_upcoming",
  EVENTS_TODAY: "events_today",
  EVENTS_SEARCH: "events_search",

  // Notification Actions
  NOTIFICATIONS_LIST: "notifications_list",
  NOTIFICATIONS_PENDING: "notifications_pending",
  NOTIFICATIONS_RECENT: "notifications_recent",
  NOTIFICATIONS_SETTINGS: "notifications_settings",

  // Priority Actions
  PRIORITY_HIGH: "priority_high",
  PRIORITY_MEDIUM: "priority_medium",
  PRIORITY_LOW: "priority_low",

  // Status Actions
  STATUS_PENDING: "status_pending",
  STATUS_IN_PROGRESS: "status_in-progress",
  STATUS_COMPLETED: "status_completed",

  // Search Actions
  SEARCH_WEB: "search_web",
  SEARCH_TASKS: "search_tasks",
  SEARCH_BIRTHDAYS: "search_birthdays",
  SEARCH_EVENTS: "search_events",
  SEARCH_ALL: "search_all",

  // General Actions
  CANCEL: "cancel",
  NOOP: "noop"
} as const;

// Callback prefixes for dynamic callback generation
export const CALLBACK_PREFIXES = {
  TASK_DETAILS: "task_details_",
  TASK_EDIT: "task_edit_",
  TASK_DELETE: "task_delete_",
  TASK_CHANGE_STATUS: "task_change_status_",
  BIRTHDAY_DETAILS: "birthday_details_",
  BIRTHDAY_EDIT: "birthday_edit_",
  BIRTHDAY_DELETE: "birthday_delete_",
  CONFIRM_DELETE_TASK: "confirm_delete_task_",
  CONFIRM_DELETE_BIRTHDAY: "confirm_delete_birthday_",
  PAGE: "_page_"
} as const;

export type CallbackType = typeof CALLBACKS[keyof typeof CALLBACKS];
export type CallbackPrefixType = typeof CALLBACK_PREFIXES[keyof typeof CALLBACK_PREFIXES];
