import { Context } from "hono";
import { Telegraf, Context as TelegrafContext } from "telegraf";
import { RepositoryFactory } from "../../../repositories";
import { AIService } from "../../../ai/ai.service";
import { TaskService } from "../../../services/task.service";
import { NotificationService } from "../../../services/notification.service";
import { BirthdayService } from "../../../services/birthday.service";
import { EventService } from "../../../services/event.service";
import { IntegrationService } from "../../../services/integration.service";

export interface BotContext extends TelegrafContext {
  session?: {
    awaitingTaskTitle?: boolean;
    awaitingTaskDescription?: boolean;
    awaitingBirthdayName?: boolean;
    awaitingBirthdayDate?: boolean;
    awaitingEventName?: boolean;
    awaitingInput?: string; // For integration inputs like 'search_query'
    searchMode?: string; // For search type: 'web', 'tasks', 'knowledge', 'all'
    tempTaskData?: {
      title?: string;
      description?: string;
      priority?: string;
      dueDate?: string;
    };
    tempBirthdayData?: {
      name?: string;
      day?: number;
      month?: number;
      year?: number;
    };
    tempEventData?: {
      name?: string;
      description?: string;
      type?: string;
    };
    currentPage?: number;
    currentFilter?: string;
    currentMenu?: string;
    editingTaskId?: string;
    editingBirthdayId?: string;
    lastAction?: string; // Track the last action for proper navigation
  };
}

export interface BotServices {
  aiService: AIService;
  taskService: TaskService;
  notificationService: NotificationService;
  birthdayService: BirthdayService;
  eventService: EventService;
  integrationService: IntegrationService;
  repositoryFactory: RepositoryFactory;
}

export class BotCore {
  private bot: Telegraf<BotContext>;
  private services: BotServices;

  constructor(botToken: string, env: Env) {
    // Initialize repository factory
    const repositoryFactory = new RepositoryFactory(env.BOT_DB);

    // Initialize AI service first
    const aiService = new AIService(env.AI);

    // Initialize services with AI service
    const taskService = new TaskService(repositoryFactory, aiService);
    const notificationService = new NotificationService(repositoryFactory);
    const birthdayService = new BirthdayService(repositoryFactory);
    const eventService = new EventService(repositoryFactory);
    const integrationService = new IntegrationService(repositoryFactory);

    this.services = {
      aiService,
      taskService,
      notificationService,
      birthdayService,
      eventService,
      integrationService,
      repositoryFactory
    };

    // Initialize Telegraf bot
    this.bot = new Telegraf<BotContext>(botToken);
    this.setupMiddleware();
  }

  private setupMiddleware() {
    // Initialize session middleware
    this.bot.use((ctx, next) => {
      if (!ctx.session) {
        ctx.session = {};
      }
      return next();
    });
  }

  async handleWebhook(c: Context) {
    try {
      const body = await c.req.json();
      console.log("Received Telegram webhook:", JSON.stringify(body, null, 2));

      // Process the Telegram message using Telegraf
      await this.bot.handleUpdate(body);

      return c.json({ status: "ok" });
    } catch (error: any) {
      console.error("Error processing Telegram webhook:", error);
      return c.json({ status: "error", message: error.message }, 500);
    }
  }

  // Send message directly to a user by their Telegram ID
  async sendMessage(chatId: string, message: string, options?: any): Promise<void> {
    try {
      await this.bot.telegram.sendMessage(chatId, message, options);
    } catch (error) {
      console.error(`Error sending message to ${chatId}:`, error);
      throw error;
    }
  }

  getBot(): Telegraf<BotContext> {
    return this.bot;
  }

  getServices(): BotServices {
    return this.services;
  }
}
