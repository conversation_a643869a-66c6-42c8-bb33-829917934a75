import { BotContext, BotServices } from "../core/core";
import { SessionManager } from "../core/session-manager";
import { MenuKeyboards } from "./menu-keyboards";
import { TaskHandlers } from "../handlers/task.handlers";
import { BirthdayHandlers } from "../handlers/birthday.handlers";
import { EventHandlers } from "../handlers/event.handlers";
import { NotificationHandlers } from "../handlers/notification.handlers";
import { IntegrationHandler } from "../../../handlers/integration.handler";
import {
  CALLBACKS,
  MENU_TITLES,
  MESSAGES,
  HELP_CONTENT,
  COMING_SOON
} from "../../constants";

export class NavigationHandler {
  private taskHandlers: TaskHandlers;
  private birthdayHandlers: BirthdayHandlers;
  private eventHandlers: EventHandlers;
  private notificationHandlers: NotificationHandlers;
  private integrationHandler: IntegrationHandler;

  constructor(private services: BotServices) {
    this.taskHandlers = new TaskHandlers(services);
    this.birthdayHandlers = new BirthdayHandlers(services);
    this.eventHandlers = new EventHandlers(services);
    this.notificationHandlers = new NotificationHandlers(services);
    this.integrationHandler = new IntegrationHandler(services.integrationService);
  }

  async handleCallback(ctx: BotContext, action: string): Promise<void> {
    try {
      // Handle main menu navigation
      if (action === CALLBACKS.MAIN_MENU) {
        await this.handleMainMenu(ctx);
        return;
      }

      // Handle cancel action
      if (action === CALLBACKS.CANCEL) {
        await this.handleCancel(ctx);
        return;
      }

      // Handle task-related callbacks
      if (action.startsWith("task")) {
        await this.handleTaskCallback(ctx, action);
        return;
      }

      // Handle birthday-related callbacks
      if (action.startsWith("birthday")) {
        await this.handleBirthdayCallback(ctx, action);
        return;
      }

      // Handle event-related callbacks
      if (action.startsWith("event")) {
        await this.handleEventCallback(ctx, action);
        return;
      }

      // Handle notification-related callbacks
      if (action.startsWith("notification")) {
        await this.handleNotificationCallback(ctx, action);
        return;
      }

      // Handle priority selection
      if (action.startsWith("priority_")) {
        const priority = action.replace("priority_", "");
        await this.taskHandlers.handlePrioritySelection(ctx, priority);
        return;
      }

      // Handle status selection
      if (action.startsWith("status_")) {
        const status = action.replace("status_", "");
        await this.taskHandlers.handleStatusSelection(ctx, status);
        return;
      }

      // Handle search-related callbacks
      if (action.startsWith("search")) {
        await this.handleSearchCallback(ctx, action);
        return;
      }

      // Handle confirmation callbacks
      if (action.startsWith("confirm_")) {
        await this.handleConfirmationCallback(ctx, action);
        return;
      }

      // Handle other menu callbacks
      await this.handleMenuCallback(ctx, action);

    } catch (error) {
      console.error(`Error handling callback ${action}:`, error);
      await ctx.editMessageText(
        "❌ An error occurred. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.MAIN_MENU)
        }
      );
    }
  }

  private async handleMainMenu(ctx: BotContext): Promise<void> {
    // Clear any ongoing sessions when returning to main menu
    SessionManager.clearSession(ctx);

    await ctx.editMessageText(
      `${MENU_TITLES.MAIN_MENU}\n\n${MESSAGES.SELECT_OPTION}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getMainMenuInlineKeyboard()
      }
    );
  }

  private async handleCancel(ctx: BotContext): Promise<void> {
    const returnMenu = SessionManager.cancelCurrentOperation(ctx);

    let menuText = `${MENU_TITLES.MAIN_MENU}\n\n${MESSAGES.SELECT_OPTION}`;
    let keyboard = MenuKeyboards.getMainMenuInlineKeyboard();

    switch (returnMenu) {
      case CALLBACKS.TASKS_MENU:
        menuText = `${MENU_TITLES.TASK_MANAGEMENT}\n\n${MESSAGES.CHOOSE_ACTION}`;
        keyboard = MenuKeyboards.getTaskMenuKeyboard();
        break;
      case CALLBACKS.BIRTHDAYS_MENU:
        menuText = `${MENU_TITLES.BIRTHDAY_MANAGEMENT}\n\n${MESSAGES.CHOOSE_ACTION}`;
        keyboard = MenuKeyboards.getBirthdayMenuKeyboard();
        break;
      case CALLBACKS.EVENTS_MENU:
        menuText = `${MENU_TITLES.CHRISTIAN_EVENTS}\n\n${MESSAGES.CHOOSE_ACTION}`;
        keyboard = MenuKeyboards.getEventsMenuKeyboard();
        break;
      case CALLBACKS.NOTIFICATIONS_MENU:
        menuText = `${MENU_TITLES.NOTIFICATION_CENTER}\n\n${MESSAGES.CHOOSE_ACTION}`;
        keyboard = MenuKeyboards.getNotificationsMenuKeyboard();
        break;
    }

    await ctx.editMessageText(menuText, {
      parse_mode: "Markdown",
      ...keyboard
    });
  }

  private async handleTaskCallback(ctx: BotContext, action: string): Promise<void> {
    if (action === "tasks_menu") {
      await this.taskHandlers.handleTasksMenu(ctx);
    } else if (action === "task_create") {
      await this.taskHandlers.handleTaskCreate(ctx);
    } else if (action === "task_list") {
      await this.taskHandlers.handleTaskList(ctx);
    } else if (action === "task_pending") {
      await this.taskHandlers.handleTaskPending(ctx);
    } else if (action === "task_completed") {
      await this.taskHandlers.handleTaskCompleted(ctx);
    } else if (action.startsWith("task_details_")) {
      const taskId = action.replace("task_details_", "");
      await this.taskHandlers.handleTaskDetails(ctx, taskId);
    } else if (action.startsWith("task_change_status_")) {
      const taskId = action.replace("task_change_status_", "");
      await this.taskHandlers.handleTaskStatusChange(ctx, taskId);
    } else if (action.startsWith("task_delete_")) {
      const taskId = action.replace("task_delete_", "");
      await this.taskHandlers.handleTaskDelete(ctx, taskId);
    } else {
      await this.handleUnknownAction(ctx, action);
    }
  }

  private async handleBirthdayCallback(ctx: BotContext, action: string): Promise<void> {
    if (action === "birthdays_menu") {
      await this.birthdayHandlers.handleBirthdaysMenu(ctx);
    } else if (action === "birthday_add") {
      await this.birthdayHandlers.handleBirthdayAdd(ctx);
    } else if (action === "birthday_list") {
      await this.birthdayHandlers.handleBirthdayList(ctx);
    } else if (action === "birthday_upcoming") {
      await this.birthdayHandlers.handleBirthdayUpcoming(ctx);
    } else if (action.startsWith("birthday_details_")) {
      const birthdayId = action.replace("birthday_details_", "");
      await this.birthdayHandlers.handleBirthdayDetails(ctx, birthdayId);
    } else if (action.startsWith("birthday_delete_")) {
      const birthdayId = action.replace("birthday_delete_", "");
      await this.birthdayHandlers.handleBirthdayDelete(ctx, birthdayId);
    } else {
      await this.handleUnknownAction(ctx, action);
    }
  }

  private async handleEventCallback(ctx: BotContext, action: string): Promise<void> {
    if (action === "events_menu") {
      await this.eventHandlers.handleEventsMenu(ctx);
    } else if (action === "events_list") {
      await this.eventHandlers.handleEventsList(ctx);
    } else if (action === "events_upcoming") {
      await this.eventHandlers.handleEventsUpcoming(ctx);
    } else if (action === "events_today") {
      await this.eventHandlers.handleEventsToday(ctx);
    } else if (action === "events_search") {
      await this.eventHandlers.handleEventsSearch(ctx);
    } else {
      await this.handleUnknownAction(ctx, action);
    }
  }

  private async handleNotificationCallback(ctx: BotContext, action: string): Promise<void> {
    if (action === "notifications_menu") {
      await this.notificationHandlers.handleNotificationsMenu(ctx);
    } else if (action === "notifications_list") {
      await this.notificationHandlers.handleNotificationsList(ctx);
    } else if (action === "notifications_pending") {
      await this.notificationHandlers.handleNotificationsPending(ctx);
    } else if (action === "notifications_recent") {
      await this.notificationHandlers.handleNotificationsRecent(ctx);
    } else if (action === "notifications_settings") {
      await this.notificationHandlers.handleNotificationsSettings(ctx);
    } else {
      await this.handleUnknownAction(ctx, action);
    }
  }

  private async handleSearchCallback(ctx: BotContext, action: string): Promise<void> {
    await this.integrationHandler.handleSearchCallback(ctx);
  }

  private async handleConfirmationCallback(ctx: BotContext, action: string): Promise<void> {
    if (action.startsWith("confirm_delete_task_")) {
      const taskId = action.replace("confirm_delete_task_", "");
      await this.taskHandlers.handleTaskDeleteConfirm(ctx, taskId);
    } else if (action.startsWith("confirm_delete_birthday_")) {
      const birthdayId = action.replace("confirm_delete_birthday_", "");
      await this.birthdayHandlers.handleBirthdayDeleteConfirm(ctx, birthdayId);
    } else {
      await this.handleUnknownAction(ctx, action);
    }
  }

  private async handleMenuCallback(ctx: BotContext, action: string): Promise<void> {
    switch (action) {
      case "help_menu":
        await this.handleHelpMenu(ctx);
        break;
      case "settings_menu":
        await this.handleSettingsMenu(ctx);
        break;
      case "calendar_menu":
        await this.handleCalendarMenu(ctx);
        break;
      case "search_menu":
        await this.handleSearchMenu(ctx);
        break;
      default:
        await this.handleUnknownAction(ctx, action);
    }
  }

  private async handleHelpMenu(ctx: BotContext): Promise<void> {
    await ctx.editMessageText(
      `${MENU_TITLES.HELP_SUPPORT}\n\n` +
      `${HELP_CONTENT.AVAILABLE_COMMANDS_SECTION}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.START}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.HELP}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.TASKS}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.BIRTHDAYS}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.EVENTS}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.NOTIFICATIONS}\n\n` +
      `${HELP_CONTENT.TIPS_SECTION}\n` +
      `${HELP_CONTENT.TIPS.join("\n")}\n\n` +
      `${HELP_CONTENT.SUPPORT_SECTION} ${HELP_CONTENT.SUPPORT_TEXT}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBackKeyboard(CALLBACKS.MAIN_MENU)
      }
    );
  }

  private async handleSettingsMenu(ctx: BotContext): Promise<void> {
    await ctx.editMessageText(
      `${MENU_TITLES.SETTINGS}\n\n` +
      `${COMING_SOON.SETTINGS}\n\n` +
      "Planned features:\n" +
      `${COMING_SOON.PLANNED_FEATURES.SETTINGS.join("\n")}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBackKeyboard(CALLBACKS.MAIN_MENU)
      }
    );
  }

  private async handleCalendarMenu(ctx: BotContext): Promise<void> {
    await ctx.editMessageText(
      `${MENU_TITLES.CALENDAR}\n\n` +
      `${COMING_SOON.CALENDAR}\n\n` +
      "Planned features:\n" +
      `${COMING_SOON.PLANNED_FEATURES.CALENDAR.join("\n")}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBackKeyboard(CALLBACKS.MAIN_MENU)
      }
    );
  }

  private async handleSearchMenu(ctx: BotContext): Promise<void> {
    await ctx.editMessageText(
      `${MENU_TITLES.SEARCH}\n\n${MESSAGES.CHOOSE_SEARCH_TYPE}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getSearchMenuKeyboard()
      }
    );
  }

  private async handleUnknownAction(ctx: BotContext, action: string): Promise<void> {
    console.warn(`Unknown callback action: ${action}`);

    await ctx.editMessageText(
      `${MESSAGES.UNKNOWN_ACTION}\n\n${MESSAGES.ACTION_NOT_RECOGNIZED.replace("{action}", action)}\n\n${MESSAGES.RETURNING_TO_MAIN}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBackKeyboard(CALLBACKS.MAIN_MENU)
      }
    );
  }
}
