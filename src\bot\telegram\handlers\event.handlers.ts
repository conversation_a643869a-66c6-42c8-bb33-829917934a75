import { BotContext, BotServices } from "../core/core";
import { SessionManager } from "../core/session-manager";
import { MenuKeyboards } from "../ui/menu-keyboards";
import {
  MESSAGES,
  ICONS,
  INPUT_VALUES,
  CALLBACKS
} from "../../constants";

export class EventHandlers {
  constructor(private services: BotServices) { }

  // Command handlers
  async handleEventsCommand(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const upcomingEvents = await this.services.eventService.getUpcomingEvents(30);

      if (upcomingEvents.length === 0) {
        await ctx.reply("✝️ No upcoming Christian events in the next 30 days.");
        return;
      }

      const eventList = upcomingEvents.map((event, index) => {
        return `${index + 1}. ✝️ *${event.name}* - ${event.eventDate.toLocaleDateString()} (${event.daysUntilEvent} days)`;
      }).join("\n");

      await ctx.reply(`✝️ *Upcoming Christian Events:*\n\n${eventList}`, { parse_mode: "Markdown" });
    } catch (error) {
      console.error("Error fetching upcoming events:", error);
      await ctx.reply("❌ Error loading upcoming events. Please try again.");
    }
  }

  async handleTodayEventsCommand(ctx: BotContext): Promise<void> {
    try {
      // Get today's events by filtering upcoming events for today
      const upcomingEvents = await this.services.eventService.getUpcomingEvents(1);
      const today = new Date();

      const todayEvents = upcomingEvents.filter(event => {
        const eventDate = event.eventDate;
        return eventDate.toDateString() === today.toDateString();
      });

      if (todayEvents.length === 0) {
        await ctx.reply(MESSAGES.NO_CHRISTIAN_EVENTS_TODAY);
        return;
      }

      const eventList = todayEvents.map((event, index) => {
        return `${index + 1}. ${ICONS.EVENT} *${event.name}*\n   ${event.type === "fixed" ? MESSAGES.FIXED_DATE_EVENT : MESSAGES.CALCULATED_EVENT}`;
      }).join("\n\n");

      await ctx.reply(`${MESSAGES.TODAY_CHRISTIAN_EVENTS}\n\n${eventList}`, { parse_mode: "Markdown" });
    } catch (error) {
      console.error("Error fetching today's events:", error);
      await ctx.reply(MESSAGES.ERROR_LOADING_EVENTS);
    }
  }

  // Callback handlers
  async handleEventsMenu(ctx: BotContext): Promise<void> {
    SessionManager.setLastAction(ctx, CALLBACKS.EVENTS_MENU);

    await ctx.editMessageText(
      "✝️ *Christian Events*\n\nChoose an action:",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getEventsMenuKeyboard()
      }
    );
  }

  async handleEventsList(ctx: BotContext): Promise<void> {
    try {
      const events = await this.services.eventService.getAllEvents();

      if (events.length === 0) {
        await ctx.editMessageText(
          "✝️ *Christian Events*\n\n" +
          "No events found in the database.",
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
          }
        );
        return;
      }

      // Group events by type for better organization
      const eventsByType = events.reduce((acc, event) => {
        if (!acc[event.type]) {
          acc[event.type] = [];
        }
        acc[event.type].push(event);
        return acc;
      }, {} as Record<string, typeof events>);

      let message = "✝️ *Christian Events*\n\n";

      Object.entries(eventsByType).forEach(([type, typeEvents]) => {
        const typeTitle = type.charAt(0).toUpperCase() + type.slice(1);
        message += `*${typeTitle} Events:*\n`;

        typeEvents.slice(0, 5).forEach((event, index) => {
          message += `${index + 1}. ${event.name}\n`;
        });

        if (typeEvents.length > 5) {
          message += `... and ${typeEvents.length - 5} more\n`;
        }
        message += "\n";
      });

      await ctx.editMessageText(message, {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
      });
    } catch (error) {
      console.error("Error fetching events:", error);
      await ctx.editMessageText(
        "❌ Error loading events. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
        }
      );
    }
  }

  async handleEventsUpcoming(ctx: BotContext): Promise<void> {
    try {
      const upcomingEvents = await this.services.eventService.getUpcomingEvents(30);

      if (upcomingEvents.length === 0) {
        await ctx.editMessageText(
          "✝️ *Upcoming Christian Events*\n\n" +
          "No events in the next 30 days.",
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
          }
        );
        return;
      }

      const eventList = upcomingEvents.map((event, index) => {
        return `${index + 1}. ✝️ *${event.name}*\n   📅 ${event.eventDate.toLocaleDateString()} (${event.daysUntilEvent} days)`;
      }).join("\n\n");

      await ctx.editMessageText(
        `✝️ *Upcoming Christian Events*\n\n${eventList}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
        }
      );
    } catch (error) {
      console.error("Error fetching upcoming events:", error);
      await ctx.editMessageText(
        "❌ Error loading upcoming events. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
        }
      );
    }
  }

  async handleEventsToday(ctx: BotContext): Promise<void> {
    try {
      // Get today's events by filtering upcoming events for today
      const upcomingEvents = await this.services.eventService.getUpcomingEvents(1);
      const today = new Date();

      const todayEvents = upcomingEvents.filter(event => {
        const eventDate = event.eventDate;
        return eventDate.toDateString() === today.toDateString();
      });

      if (todayEvents.length === 0) {
        await ctx.editMessageText(
          "✝️ *Today's Christian Events*\n\n" +
          "No events happening today.",
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
          }
        );
        return;
      }

      const eventList = todayEvents.map((event, index) => {
        return `${index + 1}. ✝️ *${event.name}*\n   ${event.type === "fixed" ? "Fixed Date Event" : "Calculated Event"}`;
      }).join("\n\n");

      await ctx.editMessageText(
        `✝️ *Today's Christian Events*\n\n${eventList}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
        }
      );
    } catch (error) {
      console.error("Error fetching today's events:", error);
      await ctx.editMessageText(
        "❌ Error loading today's events. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
        }
      );
    }
  }

  async handleEventsSearch(ctx: BotContext): Promise<void> {
    // Set up search session
    if (ctx.session) {
      ctx.session.awaitingInput = INPUT_VALUES.EVENT_SEARCH_QUERY;
      ctx.session.searchMode = "events";
    }

    await ctx.editMessageText(
      "🔍 *Search Christian Events*\n\n" +
      "Please enter your search query (event name, type, or description):",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getCancelKeyboard()
      }
    );
  }

  async handleEventSearchInput(ctx: BotContext, query: string): Promise<void> {
    try {
      const events = await this.services.eventService.getAllEvents();

      // Simple search implementation
      const searchResults = events.filter(event =>
        event.name.toLowerCase().includes(query.toLowerCase()) ||
        event.description?.toLowerCase().includes(query.toLowerCase()) ||
        event.type.toLowerCase().includes(query.toLowerCase())
      );

      // Clear search session
      SessionManager.clearIntegrationSession(ctx);

      if (searchResults.length === 0) {
        await ctx.reply(
          `🔍 *Search Results*\n\nNo events found matching "${query}".`,
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
          }
        );
        return;
      }

      const resultList = searchResults.slice(0, 10).map((event, index) => {
        return `${index + 1}. ✝️ *${event.name}*\n   Type: ${event.type}\n   ${event.description?.substring(0, 100) || "No description"}${event.description && event.description.length > 100 ? "..." : ""}`;
      }).join("\n\n");

      let message = `🔍 *Search Results for "${query}"*\n\n${resultList}`;

      if (searchResults.length > 10) {
        message += `\n\n... and ${searchResults.length - 10} more results`;
      }

      await ctx.reply(message, {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
      });
    } catch (error) {
      console.error("Error searching events:", error);
      await ctx.reply(
        "❌ Error searching events. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.EVENTS_MENU)
        }
      );
    }
  }
}
