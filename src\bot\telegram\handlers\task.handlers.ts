import { Bot<PERSON>ontext, BotServices } from "../core/core";
import { SessionManager } from "../core/session-manager";
import { MenuKeyboards } from "../ui/menu-keyboards";
import {
  MENU_TITLES,
  MESSAGES,
  ICONS,
  <PERSON>LL<PERSON><PERSON>KS,
  <PERSON>LLBACK_PREFIXES
} from "../../constants";
import { Markup } from "telegraf";

export class TaskHandlers {
  constructor(private services: BotServices) { }

  // Command handlers
  async handleTasksCommand(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    const tasks = await this.services.taskService.getUserTasks(userId, "all", 10);

    if (tasks.length === 0) {
      await ctx.reply(MESSAGES.NO_TASKS_YET);
      return;
    }

    const taskList = tasks.map((task, index) => {
      const status = task.status === "completed" ? ICONS.COMPLETED :
        task.status === "in-progress" ? ICONS.IN_PROGRESS : ICONS.PENDING;
      const priority = task.priority === "high" ? ICONS.HIGH_PRIORITY :
        task.priority === "medium" ? ICONS.MEDIUM_PRIORITY : ICONS.LOW_PRIORITY;

      return `${index + 1}. ${status} ${priority} ${task.title}`;
    }).join("\n");

    await ctx.reply(`${MESSAGES.YOUR_TASKS}\n\n${taskList}`, { parse_mode: "Markdown" });
  }

  async handlePendingTasksCommand(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    const tasks = await this.services.taskService.getUserTasks(userId, "pending", 10);

    if (tasks.length === 0) {
      await ctx.reply(MESSAGES.NO_PENDING_TASKS);
      return;
    }

    const taskList = tasks.map((task, index) => {
      const priority = task.priority === "high" ? ICONS.HIGH_PRIORITY :
        task.priority === "medium" ? ICONS.MEDIUM_PRIORITY : ICONS.LOW_PRIORITY;
      return `${index + 1}. ${priority} ${task.title}`;
    }).join("\n");

    await ctx.reply(`${MESSAGES.PENDING_TASKS}\n\n${taskList}`, { parse_mode: "Markdown" });
  }

  async handleCompletedTasksCommand(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    const tasks = await this.services.taskService.getUserTasks(userId, "completed", 10);

    if (tasks.length === 0) {
      await ctx.reply(MESSAGES.NO_COMPLETED_TASKS);
      return;
    }

    const taskList = tasks.map((task, index) => {
      return `${index + 1}. ${ICONS.COMPLETED} ${task.title}`;
    }).join("\n");

    await ctx.reply(`${MESSAGES.COMPLETED_TASKS}\n\n${taskList}`, { parse_mode: "Markdown" });
  }

  // Callback handlers
  async handleTasksMenu(ctx: BotContext): Promise<void> {
    SessionManager.setLastAction(ctx, CALLBACKS.TASKS_MENU);

    await ctx.editMessageText(
      `${MENU_TITLES.TASK_MANAGEMENT}\n\n${MESSAGES.CHOOSE_ACTION}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getTaskMenuKeyboard()
      }
    );
  }

  async handleTaskCreate(ctx: BotContext): Promise<void> {
    SessionManager.startTaskCreation(ctx);

    await ctx.editMessageText(
      `${MENU_TITLES.CREATE_NEW_TASK}\n\n${MESSAGES.ENTER_TASK_TITLE}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getCancelKeyboard()
      }
    );
  }

  async handleTaskList(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const tasks = await this.services.taskService.getUserTasks(userId, "all", 10);

      if (tasks.length === 0) {
        await ctx.editMessageText(
          `${MESSAGES.YOUR_TASKS}\n\n${MESSAGES.NO_TASKS_YET}`,
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
          }
        );
        return;
      }

      const taskButtons = tasks.map((task, index) => {
        const status = task.status === "completed" ? ICONS.COMPLETED :
          task.status === "in-progress" ? ICONS.IN_PROGRESS : ICONS.PENDING;
        const priority = task.priority === "high" ? ICONS.HIGH_PRIORITY :
          task.priority === "medium" ? ICONS.MEDIUM_PRIORITY : ICONS.LOW_PRIORITY;

        return [Markup.button.callback(
          `${status} ${priority} ${task.title.substring(0, 30)}${task.title.length > 30 ? MESSAGES.ELLIPSIS : ""}`,
          `${CALLBACK_PREFIXES.TASK_DETAILS}${task.id}`
        )];
      });

      taskButtons.push([Markup.button.callback("🔙 Back", CALLBACKS.TASKS_MENU)]);

      await ctx.editMessageText(
        `${MESSAGES.YOUR_TASKS}\n\n${MESSAGES.SELECT_TASK_DETAILS}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: taskButtons
          }
        }
      );
    } catch (error) {
      console.error("Error fetching tasks:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_LOADING_TASKS,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
        }
      );
    }
  }

  async handleTaskPending(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const tasks = await this.services.taskService.getUserTasks(userId, "pending", 10);

      if (tasks.length === 0) {
        await ctx.editMessageText(
          `${MESSAGES.PENDING_TASKS}\n\n${MESSAGES.NO_PENDING_TASKS}`,
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
          }
        );
        return;
      }

      const taskButtons = tasks.map((task) => {
        const priority = task.priority === "high" ? ICONS.HIGH_PRIORITY :
          task.priority === "medium" ? ICONS.MEDIUM_PRIORITY : ICONS.LOW_PRIORITY;

        return [Markup.button.callback(
          `${priority} ${task.title.substring(0, 30)}${task.title.length > 30 ? MESSAGES.ELLIPSIS : ""}`,
          `${CALLBACK_PREFIXES.TASK_DETAILS}${task.id}`
        )];
      });

      taskButtons.push([Markup.button.callback("🔙 Back", CALLBACKS.TASKS_MENU)]);

      await ctx.editMessageText(
        `${MESSAGES.PENDING_TASKS}\n\n${MESSAGES.SELECT_TASK_DETAILS}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: taskButtons
          }
        }
      );
    } catch (error) {
      console.error("Error fetching pending tasks:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_LOADING_TASKS,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
        }
      );
    }
  }

  async handleTaskCompleted(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const tasks = await this.services.taskService.getUserTasks(userId, "completed", 10);

      if (tasks.length === 0) {
        await ctx.editMessageText(
          `${MESSAGES.COMPLETED_TASKS}\n\n${MESSAGES.NO_COMPLETED_TASKS}`,
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
          }
        );
        return;
      }

      const taskButtons = tasks.map((task) => {
        return [Markup.button.callback(
          `${ICONS.COMPLETED} ${task.title.substring(0, 30)}${task.title.length > 30 ? MESSAGES.ELLIPSIS : ""}`,
          `${CALLBACK_PREFIXES.TASK_DETAILS}${task.id}`
        )];
      });

      taskButtons.push([Markup.button.callback("🔙 Back", CALLBACKS.TASKS_MENU)]);

      await ctx.editMessageText(
        `${MESSAGES.COMPLETED_TASKS}\n\n${MESSAGES.SELECT_TASK_DETAILS}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: taskButtons
          }
        }
      );
    } catch (error) {
      console.error("Error fetching completed tasks:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_LOADING_TASKS,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
        }
      );
    }
  }

  async handleTaskDetails(ctx: BotContext, taskId: string): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const task = await this.services.taskService.getTask(taskId, userId);

      if (!task) {
        await ctx.editMessageText(
          MESSAGES.ERROR_TASK_NOT_FOUND,
          {
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASK_LIST)
          }
        );
        return;
      }

      const status = task.status === "completed" ? MESSAGES.STATUS_COMPLETED_TEXT :
        task.status === "in-progress" ? MESSAGES.STATUS_IN_PROGRESS_TEXT : MESSAGES.STATUS_PENDING_TEXT;
      const priority = task.priority === "high" ? MESSAGES.PRIORITY_HIGH_TEXT :
        task.priority === "medium" ? MESSAGES.PRIORITY_MEDIUM_TEXT : MESSAGES.PRIORITY_LOW_TEXT;

      let message = `${MESSAGES.TASK_DETAILS_TITLE}\n\n` +
        `${MESSAGES.TITLE_LABEL} ${task.title}\n` +
        `${MESSAGES.STATUS_LABEL} ${status}\n` +
        `${MESSAGES.PRIORITY_LABEL} ${priority}\n`;

      if (task.description) {
        message += `${MESSAGES.DESCRIPTION_LABEL} ${task.description}\n`;
      }

      if (task.dueDate) {
        message += `${MESSAGES.DUE_DATE_LABEL} ${new Date(task.dueDate).toLocaleDateString()}\n`;
      }

      message += `${MESSAGES.CREATED_LABEL} ${new Date(task.createdAt).toLocaleDateString()}`;

      await ctx.editMessageText(message, {
        parse_mode: "Markdown",
        ...MenuKeyboards.getTaskActionKeyboard(taskId)
      });
    } catch (error) {
      console.error("Error fetching task details:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_LOADING_TASKS,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASK_LIST)
        }
      );
    }
  }

  async handlePrioritySelection(ctx: BotContext, priority: string): Promise<void> {
    if (!ctx.session?.tempTaskData?.title) {
      await ctx.editMessageText(
        MESSAGES.ERROR_SESSION_EXPIRED,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
        }
      );
      return;
    }

    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      const task = await this.services.taskService.createTask(
        userId,
        ctx.session.tempTaskData.title!,
        ctx.session.tempTaskData.description,
        priority as "high" | "medium" | "low"
      );

      // Clear session data
      SessionManager.clearTaskSession(ctx);

      await ctx.editMessageText(
        `${MESSAGES.TASK_CREATED_SUCCESS}\n\n` +
        `${MESSAGES.TITLE_LABEL} ${task.title}\n` +
        `${MESSAGES.PRIORITY_LABEL} ${priority}\n` +
        `${MESSAGES.STATUS_LABEL} ${MESSAGES.STATUS_PENDING_TEXT}\n\n` +
        `${MESSAGES.TASK_ADDED_TO_LIST}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [[
              { text: MESSAGES.VIEW_TASKS_BUTTON, callback_data: CALLBACKS.TASK_LIST },
              { text: MESSAGES.MAIN_MENU_BUTTON, callback_data: CALLBACKS.MAIN_MENU }
            ]]
          }
        }
      );
    } catch (error) {
      console.error("Error creating task:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_CREATING_TASK,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASKS_MENU)
        }
      );
    }
  }

  async handleTaskStatusChange(ctx: BotContext, taskId: string): Promise<void> {
    await ctx.editMessageText(
      `${MENU_TITLES.CHANGE_TASK_STATUS}\n\n${MESSAGES.SELECT_NEW_STATUS}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getTaskStatusKeyboard()
      }
    );

    // Store the task ID for the status change
    if (ctx.session) {
      ctx.session.editingTaskId = taskId;
    }
  }

  async handleStatusSelection(ctx: BotContext, status: string): Promise<void> {
    const taskId = ctx.session?.editingTaskId;
    if (!taskId) {
      await ctx.editMessageText(
        "❌ Session expired. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASK_LIST)
        }
      );
      return;
    }

    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      await this.services.taskService.updateTask(taskId, userId, { status: status as any });

      // Clear editing session
      if (ctx.session) {
        ctx.session.editingTaskId = undefined;
      }

      const statusText = status === "completed" ? "✅ Completed" :
        status === "in-progress" ? "🔄 In Progress" : "⏳ Pending";

      await ctx.editMessageText(
        `✅ *Task Status Updated!*\n\nNew status: ${statusText}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [[
              { text: MESSAGES.VIEW_TASK_BUTTON, callback_data: `${CALLBACK_PREFIXES.TASK_DETAILS}${taskId}` },
              { text: MESSAGES.ALL_TASKS_BUTTON, callback_data: CALLBACKS.TASK_LIST }
            ]]
          }
        }
      );
    } catch (error) {
      console.error("Error updating task status:", error);
      await ctx.editMessageText(
        "❌ Error updating task status. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASK_LIST)
        }
      );
    }
  }

  async handleTaskDelete(ctx: BotContext, taskId: string): Promise<void> {
    await ctx.editMessageText(
      "🗑️ *Delete Task*\n\nAre you sure you want to delete this task? This action cannot be undone.",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getConfirmationKeyboard(`${CALLBACK_PREFIXES.CONFIRM_DELETE_TASK}${taskId}`, CALLBACKS.TASK_LIST)
      }
    );
  }

  async handleTaskDeleteConfirm(ctx: BotContext, taskId: string): Promise<void> {
    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      // Get task details before deletion for confirmation message
      const task = await this.services.taskService.getTask(taskId, userId);

      if (!task) {
        await ctx.editMessageText(
          "❌ Task not found.",
          {
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASK_LIST)
          }
        );
        return;
      }

      // Delete the task
      await this.services.taskService.deleteTask(taskId, userId);

      await ctx.editMessageText(
        `🗑️ *Task Deleted*\n\nTask "${task.title}" has been permanently deleted.`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [[
              { text: MESSAGES.VIEW_TASKS_BUTTON, callback_data: CALLBACKS.TASK_LIST },
              { text: MESSAGES.MAIN_MENU_BUTTON, callback_data: CALLBACKS.MAIN_MENU }
            ]]
          }
        }
      );
    } catch (error) {
      console.error("Error deleting task:", error);
      await ctx.editMessageText(
        "❌ Error deleting task. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.TASK_LIST)
        }
      );
    }
  }
}
