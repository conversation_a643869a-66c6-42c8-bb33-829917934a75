import { <PERSON>Hand<PERSON> } from "../../../handlers/integration.handler";
import {
  BUTTONS,
  CALLBACKS,
  INPUT_VALUES
} from "../../constants";
import { BotContext, BotServices } from "../core/core";
import { SessionManager } from "../core/session-manager";
import { MenuKeyboards } from "../ui/menu-keyboards";

export class MessageHandler {
  constructor(private services: BotServices) { }

  async handleTextMessage(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    const messageText = (ctx.message as any)?.text;

    if (!userId || !messageText) {
      return;
    }

    console.log("Current Session:", JSON.stringify(ctx.session, null, 2));

    // Handle integration search input
    if (ctx.session?.awaitingInput === INPUT_VALUES.SEARCH_QUERY) {
      const integrationHandler = new IntegrationHandler(this.services.integrationService);
      await integrationHandler.handleSearchInput(ctx);
      return;
    }

    // Handle event search input
    if (ctx.session?.awaitingInput === INPUT_VALUES.EVENT_SEARCH_QUERY) {
      const EventHandlers = await import("./event.handlers");
      const eventHandlers = new EventHandlers.EventHandlers(this.services);
      await eventHandlers.handleEventSearchInput(ctx, messageText);
      return;
    }

    // Handle task creation flow
    if (ctx.session?.awaitingTaskTitle) {
      await this.handleTaskTitleInput(ctx, messageText);
      return;
    }

    if (ctx.session?.awaitingTaskDescription) {
      await this.handleTaskDescriptionInput(ctx, messageText);
      return;
    }

    // Handle birthday creation flow
    if (ctx.session?.awaitingBirthdayName) {
      await this.handleBirthdayNameInput(ctx, messageText);
      return;
    }

    if (ctx.session?.awaitingBirthdayDate) {
      await this.handleBirthdayDateInput(ctx, messageText);
      return;
    }

    // Handle event creation flow
    if (ctx.session?.awaitingEventName) {
      await this.handleEventNameInput(ctx, messageText);
      return;
    }

    // If user is not in any specific flow, check if this is a forwarded message
    // Only create tasks from forwarded messages or explicit task creation
    const message = ctx.message as any;
    const isForwarded = !!(
      message.forward_from ||
      message.forward_from_chat ||
      message.forward_date ||
      message.forward_origin
    );

    if (isForwarded) {
      await this.createTaskFromForwardedMessage(ctx, messageText);
    } else {
      // For regular messages, suggest creating a task instead of auto-creating
      await ctx.reply(
        "💬 I received your message! Would you like me to:\n\n" +
        "📝 Create a task from this message\n" +
        "🎂 Add a birthday\n" +
        "✝️ Add an event\n" +
        "🔍 Search for something\n\n" +
        "Use the menu below to choose an action:",
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getMainMenuInlineKeyboard()
        }
      );
    }
  }

  private async handleTaskTitleInput(ctx: BotContext, title: string): Promise<void> {
    if (!ctx.session?.tempTaskData) {
      ctx.session!.tempTaskData = {};
    }

    ctx.session!.tempTaskData.title = title;
    ctx.session!.awaitingTaskTitle = false;
    ctx.session!.awaitingTaskDescription = true;

    await ctx.reply(
      "📝 *Create New Task*\n\n" +
      `Title: ${title}\n\n` +
      "Now please provide a description for the task (or type 'skip' to skip):",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getCancelKeyboard()
      }
    );
  }

  private async handleTaskDescriptionInput(ctx: BotContext, description: string): Promise<void> {
    if (!ctx.session?.tempTaskData) {
      return;
    }

    if (description.toLowerCase() !== "skip") {
      ctx.session!.tempTaskData.description = description;
    }

    ctx.session!.awaitingTaskDescription = false;

    await ctx.reply(
      "📝 *Create New Task*\n\n" +
      `Title: ${ctx.session.tempTaskData.title}\n` +
      `Description: ${ctx.session.tempTaskData.description || "None"}\n\n` +
      "Please select the priority level:",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getPriorityKeyboard()
      }
    );
  }

  private async handleBirthdayNameInput(ctx: BotContext, name: string): Promise<void> {
    if (!ctx.session?.tempBirthdayData) {
      ctx.session!.tempBirthdayData = {};
    }

    ctx.session!.tempBirthdayData.name = name;
    ctx.session!.awaitingBirthdayName = false;
    ctx.session!.awaitingBirthdayDate = true;

    await ctx.reply(
      "🎂 *Add New Birthday*\n\n" +
      `Name: ${name}\n\n` +
      "Now please send the birthday date in format DD/MM or DD/MM/YYYY:",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getCancelKeyboard()
      }
    );
  }

  private async handleBirthdayDateInput(ctx: BotContext, dateText: string): Promise<void> {
    if (!ctx.session?.tempBirthdayData?.name) {
      return;
    }

    const dateMatch = dateText.match(/^(\d{1,2})\/(\d{1,2})(?:\/(\d{4}))?$/);

    if (!dateMatch) {
      await ctx.reply(
        "❌ Invalid date format. Please use DD/MM or DD/MM/YYYY format.\n\nExample: 25/12 or 25/12/1990",
        {
          ...MenuKeyboards.getCancelKeyboard()
        }
      );
      return;
    }

    const day = parseInt(dateMatch[1]);
    const month = parseInt(dateMatch[2]);
    const year = dateMatch[3] ? parseInt(dateMatch[3]) : undefined;

    // Validate date
    if (day < 1 || day > 31 || month < 1 || month > 12) {
      await ctx.reply(
        "❌ Invalid date. Please check the day (1-31) and month (1-12).",
        {
          ...MenuKeyboards.getCancelKeyboard()
        }
      );
      return;
    }

    // Validate specific month/day combinations
    const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    if (day > daysInMonth[month - 1]) {
      await ctx.reply(
        "❌ Invalid date for this month. Please check the day.",
        {
          ...MenuKeyboards.getCancelKeyboard()
        }
      );
      return;
    }

    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      const birthday = await this.services.birthdayService.addBirthday(
        userId,
        ctx.session.tempBirthdayData.name!,
        day,
        month,
        year,
        [1, 7] // Default reminder days
      );

      // Clear session data
      SessionManager.clearBirthdaySession(ctx);

      const dateStr = year ? `${day}/${month}/${year}` : `${day}/${month}`;
      await ctx.reply(
        "🎂 *Birthday Added Successfully!*\n\n" +
        `Name: ${birthday.name}\n` +
        `Date: ${dateStr}\n` +
        "Reminders: 1 day and 7 days before\n\n" +
        "✅ Birthday has been added to your list!",
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [[
              { text: BUTTONS.VIEW_BIRTHDAYS, callback_data: CALLBACKS.BIRTHDAY_LIST },
              { text: BUTTONS.MAIN_MENU, callback_data: CALLBACKS.MAIN_MENU }
            ]]
          }
        }
      );
    } catch (error) {
      console.error("Error creating birthday:", error);
      await ctx.reply(
        "❌ Error creating birthday. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard("birthdays_menu")
        }
      );
    }
  }

  private async handleEventNameInput(ctx: BotContext, name: string): Promise<void> {
    // Event creation logic would go here
    await ctx.reply(
      "✝️ Event creation is not yet implemented. Please use the existing events.",
      {
        ...MenuKeyboards.getBackKeyboard("events_menu")
      }
    );
  }

  private async createTaskFromForwardedMessage(ctx: BotContext, messageText: string): Promise<void> {
    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      const message = ctx.message as any;

      // Collect forwarded message metadata
      const forwardedMetadata: any = {};
      if (message.forward_date) {
        forwardedMetadata.forwardDate = new Date(message.forward_date * 1000).toISOString();
      }

      if (message.forward_from) {
        forwardedMetadata.forwardFrom = {
          id: message.forward_from.id,
          firstName: message.forward_from.first_name,
          lastName: message.forward_from.last_name,
          username: message.forward_from.username,
          isBot: message.forward_from.is_bot
        };
      }

      if (message.forward_from_chat) {
        forwardedMetadata.forwardFromChat = {
          id: message.forward_from_chat.id,
          title: message.forward_from_chat.title,
          type: message.forward_from_chat.type,
          username: message.forward_from_chat.username
        };
      }

      // Create task from forwarded message with AI enhancement
      const task = await this.services.taskService.createTaskFromMessage(
        userId,
        messageText,
        {
          source: "forwarded",
          messageId: ctx.message?.message_id,
          originalMessage: messageText,
          useAI: true,
          enhancementLevel: "full",
          ...forwardedMetadata
        }
      );

      let successMessage = "✅ Task created successfully from forwarded message!\n\n" +
        `📝 *Title:* ${task.title}\n` +
        `📊 *Priority:* ${task.priority}\n` +
        `📅 *Status:* ${task.status}`;

      if (task.dueDate) {
        successMessage += `\n⏰ *Due:* ${new Date(task.dueDate).toLocaleDateString()}`;
      }

      // Add forwarded message context
      successMessage += "\n\n📤 *Source:* Forwarded message";

      if (message.forward_from && !message.forward_from.is_bot) {
        const fromName = message.forward_from.first_name +
          (message.forward_from.last_name ? ` ${message.forward_from.last_name}` : "");
        successMessage += `\n👤 *From:* ${fromName}`;
      }

      if (message.forward_from_chat) {
        successMessage += `\n💬 *From Chat:* ${message.forward_from_chat.title}`;
      }

      if (message.forward_date) {
        const forwardDate = new Date(message.forward_date * 1000);
        successMessage += `\n📅 *Originally sent:* ${forwardDate.toLocaleDateString()} ${forwardDate.toLocaleTimeString()}`;
      }

      await ctx.reply(successMessage, { parse_mode: "Markdown" });
    } catch (error) {
      console.error("Error creating task from forwarded message:", error);
      await ctx.reply("❌ Sorry, there was an error creating your task. Please try again.");
    }
  }
}
