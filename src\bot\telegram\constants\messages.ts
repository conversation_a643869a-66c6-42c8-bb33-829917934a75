export const MESSAGES = {
  // Welcome Messages
  WELCOME_PREFIX: "🎉 *Welcome to Novers Assistant*",
  WELCOME_DESCRIPTION: "I'm your personal productivity companion. Here's what I can do:",

  // Success Messages
  TASK_CREATED_SUCCESS: "✅ *Task Created Successfully!*",
  BIRTHDAY_ADDED_SUCCESS: "🎂 *Birthday Added Successfully!*",
  TASK_STATUS_UPDATED: "✅ *Task Status Updated!*",
  TASK_DELETED: "🗑️ *Task Deleted*",
  BIRTHDAY_DELETED: "🗑️ *Birthday Deleted*",
  NOTIFICATION_CANCELLED: "✅ *Notification Cancelled*",
  NOTIFICATION_SNOOZED: "⏰ *Notification Snoozed*",

  // Error Messages
  ERROR_OCCURRED: "❌ An error occurred. Please try again.",
  ERROR_LOADING_TASKS: "❌ Error loading tasks. Please try again.",
  ERROR_LOADING_BIRTHDAYS: "❌ Error loading birthdays. Please try again.",
  ERROR_LOADING_EVENTS: "❌ Error loading events. Please try again.",
  ERROR_LOADING_NOTIFICATIONS: "❌ Error loading notifications. Please try again.",
  ERROR_CREATING_TASK: "❌ Error creating task. Please try again.",
  ERROR_CREATING_BIRTHDAY: "❌ Error creating birthday. Please try again.",
  ERROR_UPDATING_TASK: "❌ Error updating task status. Please try again.",
  ERROR_DELETING_TASK: "❌ Error deleting task. Please try again.",
  ERROR_DELETING_BIRTHDAY: "❌ Error deleting birthday. Please try again.",
  ERROR_TASK_NOT_FOUND: "❌ Task not found.",
  ERROR_BIRTHDAY_NOT_FOUND: "❌ Birthday not found.",
  ERROR_USER_NOT_FOUND: "❌ User not found.",
  ERROR_SESSION_EXPIRED: "❌ Task creation session expired. Please start again.",
  ERROR_BIRTHDAY_SESSION_EXPIRED: "❌ Session expired. Please try again.",
  ERROR_INVALID_DATE_FORMAT: "❌ Invalid date format. Please use DD/MM or DD/MM/YYYY format.",
  ERROR_INVALID_DATE: "❌ Invalid date. Please check the day (1-31) and month (1-12).",
  ERROR_INVALID_DATE_MONTH: "❌ Invalid date for this month. Please check the day.",

  // Info Messages
  NO_TASKS_YET: "📝 You have no tasks yet. Use the menu to create one!",
  NO_PENDING_TASKS: "✅ No pending tasks! Great job!",
  NO_COMPLETED_TASKS: "📝 No completed tasks yet. Keep working!",
  NO_BIRTHDAYS_FOUND: "No birthdays found. Add your first birthday!",
  NO_UPCOMING_BIRTHDAYS: "🎉 No upcoming birthdays in the next 30 days.",
  NO_UPCOMING_BIRTHDAYS_30_DAYS: "No birthdays in the next 30 days.",
  NO_CHRISTIAN_EVENTS_TODAY: "✝️ No Christian events happening today.",
  NO_UPCOMING_CHRISTIAN_EVENTS: "✝️ No upcoming Christian events in the next 30 days.",
  NO_EVENTS_30_DAYS: "No events in the next 30 days.",
  NO_NOTIFICATIONS_FOUND: "No notifications found.",
  NO_PENDING_NOTIFICATIONS: "No pending notifications.",
  NO_RECENT_NOTIFICATIONS: "No recent notifications.",
  NO_EVENTS_DATABASE: "No events found in the database.",
  NO_EVENTS_TODAY: "No events happening today.",

  // Instruction Messages
  ENTER_TASK_TITLE: "Please enter the task title:",
  ENTER_TASK_DESCRIPTION: "Now please provide a description for the task (or type 'skip' to skip):",
  ENTER_PERSON_NAME: "Please enter the person's name:",
  ENTER_BIRTHDAY_DATE: "Now please send the birthday date in format DD/MM or DD/MM/YYYY:",
  SELECT_PRIORITY: "Please select the priority level:",
  SELECT_NEW_STATUS: "Select the new status:",
  ENTER_SEARCH_QUERY: "Please enter your search query (event name, type, or description):",

  // Confirmation Messages
  CONFIRM_DELETE_TASK: "Are you sure you want to delete this task? This action cannot be undone.",
  CONFIRM_DELETE_BIRTHDAY: "Are you sure you want to delete this birthday? This action cannot be undone.",

  // List Headers
  YOUR_TASKS: "📋 *Your Tasks:*",
  PENDING_TASKS: "⏳ *Pending Tasks:*",
  COMPLETED_TASKS: "✅ *Completed Tasks:*",
  YOUR_BIRTHDAYS: "🎂 *Your Birthdays*",
  UPCOMING_BIRTHDAYS: "🎉 *Upcoming Birthdays:*",
  UPCOMING_BIRTHDAYS_TITLE: "🎉 *Upcoming Birthdays*",
  UPCOMING_CHRISTIAN_EVENTS: "✝️ *Upcoming Christian Events:*",
  UPCOMING_CHRISTIAN_EVENTS_TITLE: "✝️ *Upcoming Christian Events*",
  TODAY_CHRISTIAN_EVENTS: "✝️ *Today's Christian Events:*",
  TODAY_CHRISTIAN_EVENTS_TITLE: "✝️ *Today's Christian Events*",
  YOUR_NOTIFICATIONS: "🔔 *Your Notifications*",
  PENDING_NOTIFICATIONS: "⏰ *Pending Notifications*",
  RECENT_NOTIFICATIONS: "✅ *Recent Notifications*",
  CHRISTIAN_EVENTS: "✝️ *Christian Events*",

  // Action Prompts
  CHOOSE_ACTION: "Choose an action:",
  SELECT_OPTION: "Select an option:",
  SELECT_TASK_DETAILS: "Select a task to view details:",
  SELECT_BIRTHDAY_DETAILS: "Select a birthday to view details:",
  CHOOSE_SEARCH_TYPE: "Choose what to search:",

  // Unknown Action
  UNKNOWN_ACTION: "❌ *Unknown Action*",
  ACTION_NOT_RECOGNIZED: "The action \"{action}\" is not recognized.",
  RETURNING_TO_MAIN: "Returning to main menu...",

  // Additional extracted messages
  BIRTHDAY_DETAILS_TITLE: "🎂 *Birthday Details*",
  CURRENT_AGE: "*Current Age:*",
  NEXT_AGE: "*Next Age:*",
  NAME_LABEL: "*Name:*",
  DATE_LABEL: "*Date:*",
  NEXT_BIRTHDAY: "*Next Birthday:*",
  DAYS_UNTIL: "*Days Until:*",
  REMINDERS_LABEL: "*Reminders:*",
  STATUS_LABEL: "*Status:*",
  ACTIVE_STATUS: "Active",
  INACTIVE_STATUS: "Inactive",
  DAYS_BEFORE: "days before",
  BIRTHDAY_ADDED_TO_LIST: "✅ Birthday has been added to your list!",
  REMINDERS_DEFAULT: "Reminders: 1 day and 7 days before",

  // Event type descriptions
  FIXED_DATE_EVENT: "Fixed Date Event",
  CALCULATED_EVENT: "Calculated Event",

  // Time and date related
  DAYS_SUFFIX: "days",
  DAY_SUFFIX: "day",

  // Notification settings
  TIMEZONE_LABEL: "🌍 *Timezone:*",
  DEFAULT_TIME_LABEL: "⏰ *Default Time:*",
  MODE_LABEL: "💬 *Mode:*",

  // Statistics
  STATISTICS: "📊 *Statistics:*",
  PENDING: "Pending:",
  RECENT: "Recent:",
  TOTAL: "Total:",

  // Task details and status text
  TASK_DETAILS_TITLE: "📝 *Task Details*",
  TITLE_LABEL: "*Title:*",
  PRIORITY_LABEL: "*Priority:*",
  DESCRIPTION_LABEL: "*Description:*",
  DUE_DATE_LABEL: "*Due Date:*",
  CREATED_LABEL: "*Created:*",

  // Status text with icons
  STATUS_COMPLETED_TEXT: "✅ Completed",
  STATUS_IN_PROGRESS_TEXT: "🔄 In Progress",
  STATUS_PENDING_TEXT: "⏳ Pending",

  // Priority text with icons
  PRIORITY_HIGH_TEXT: "🔴 High",
  PRIORITY_MEDIUM_TEXT: "🟡 Medium",
  PRIORITY_LOW_TEXT: "🟢 Low",

  // Task creation success
  TASK_ADDED_TO_LIST: "Your task has been added to your list!",

  // Button text
  VIEW_TASKS_BUTTON: "📋 View Tasks",
  VIEW_TASK_BUTTON: "📋 View Task",
  ALL_TASKS_BUTTON: "📝 All Tasks",
  MAIN_MENU_BUTTON: "🏠 Main Menu",
  VIEW_BIRTHDAYS_BUTTON: "🎂 View Birthdays",

  // Birthday specific messages
  SELECT_BIRTHDAY_TO_VIEW: "Select a birthday to view details:",
  BIRTHDAY_DETAILS_HEADER: "🎂 *Birthday Details*",
  BIRTHDAY_DELETED_SUCCESS: "🗑️ *Birthday Deleted*",
  BIRTHDAY_DELETED_MESSAGE: "Birthday for \"{name}\" has been permanently deleted.",

  // Ellipsis for truncated text
  ELLIPSIS: "..."
} as const;

// Wizard Messages
export const WIZARD_MESSAGES = {
  TASK_CREATION: {
    TITLE_PROMPT: "📝 *Create New Task* (Step 1/6)\n\nPlease enter the task title:",
    DESCRIPTION_PROMPT: "📝 *Create New Task* (Step 2/6)\n\nPlease enter a description for your task (optional):",
    PRIORITY_PROMPT: "📝 *Create New Task* (Step 3/6)\n\nSelect the priority level:",
    DUE_DATE_PROMPT: "📝 *Create New Task* (Step 4/6)\n\nSet a due date (optional):",
    AI_ENHANCEMENT_PROMPT: "📝 *Create New Task* (Step 5/6)\n\nWould you like AI to enhance your task?",
    CONFIRMATION_PROMPT: "📝 *Create New Task* (Step 6/6)\n\nReview your task:"
  },
  BIRTHDAY_CREATION: {
    NAME_PROMPT: "🎂 *Add Birthday* (Step 1/4)\n\nPlease enter the person's name:",
    DATE_PROMPT: "🎂 *Add Birthday* (Step 2/4)\n\nPlease enter the birth date (DD/MM or DD/MM/YYYY):",
    REMINDER_PROMPT: "🎂 *Add Birthday* (Step 3/4)\n\nWhen would you like to be reminded?",
    CONFIRMATION_PROMPT: "🎂 *Add Birthday* (Step 4/4)\n\nReview the birthday:"
  },
  SETTINGS: {
    CATEGORY_PROMPT: "⚙️ *Settings*\n\nWhat would you like to configure?",
    VALUE_PROMPT: "⚙️ *Settings*\n\nSelect your preference:",
    CONFIRMATION_PROMPT: "⚙️ *Settings*\n\nConfirm your changes:"
  }
} as const;

// Validation Messages
export const VALIDATION_MESSAGES = {
  TASK_TITLE_REQUIRED: "❌ Task title is required. Please enter a title:",
  TASK_TITLE_TOO_LONG: "❌ Task title is too long (max 100 characters). Please enter a shorter title:",
  BIRTHDAY_NAME_REQUIRED: "❌ Name is required. Please enter the person's name:",
  BIRTHDAY_DATE_INVALID: "❌ Invalid date format. Please use DD/MM or DD/MM/YYYY format:",
  BIRTHDAY_DATE_FUTURE: "❌ Birth date cannot be in the future. Please enter a valid date:",
  SETTINGS_INVALID_CHOICE: "❌ Invalid choice. Please select from the available options:"
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  TASK_CREATED: "✅ *Task Created Successfully!*\n\nYour task has been added to your list.",
  BIRTHDAY_ADDED: "🎂 *Birthday Added Successfully!*\n\nReminder has been set up.",
  SETTINGS_UPDATED: "⚙️ *Settings Updated!*\n\nYour preferences have been saved.",
  OPERATION_CANCELLED: "❌ *Operation Cancelled*\n\nNo changes were made."
} as const;

// Subscription Messages
export const SUBSCRIPTION_MESSAGES = {
  PLAN_SELECTION: "💳 *Choose Your Plan*\n\nSelect the subscription plan that works for you:",
  PAYMENT_PROCESSING: "💳 *Processing Payment*\n\nPlease wait while we process your payment...",
  PAYMENT_SUCCESS: "✅ *Payment Successful!*\n\nWelcome to Novers Premium!",
  PAYMENT_FAILED: "❌ *Payment Failed*\n\nPlease try again or contact support.",
  SUBSCRIPTION_ACTIVE: "✅ *Subscription Active*\n\nYou have access to all premium features."
} as const;

export type MessageType = typeof MESSAGES[keyof typeof MESSAGES];
