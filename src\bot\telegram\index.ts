import { Context } from "hono";
import { message } from "telegraf/filters";
import {
  BUTTONS,
  COMMANDS,
  FEATURES,
  HELP_CONTENT,
  MENU_TITLES,
  MESSAGES
} from "../constants";
import { BotContext, BotCore } from "./core/core";
import { BirthdayHandlers } from "./handlers/birthday.handlers";
import { EventHandlers } from "./handlers/event.handlers";
import { MessageHandler } from "./handlers/message.handler";
import { NotificationHandlers } from "./handlers/notification.handlers";
import { TaskHandlers } from "./handlers/task.handlers";
import { MenuKeyboards } from "./ui/menu-keyboards";
import { NavigationHandler } from "./ui/navigation-handler";

export class TelegramBot {
  private botCore: BotCore;
  private messageHandler: MessageHandler;
  private taskHandlers: TaskHandlers;
  private birthdayHandlers: BirthdayHandlers;
  private eventHandlers: EventHandlers;
  private notificationHandlers: NotificationHandlers;
  private navigationHandler: NavigationHandler;

  constructor(env: Env) {
    const botToken = env.TELEGRAM_BOT_TOKEN || env.BOT_TOKEN;

    // Initialize core bot
    this.botCore = new BotCore(botToken, env);
    const services = this.botCore.getServices();

    // Initialize handlers
    this.messageHandler = new MessageHandler(services);
    this.taskHandlers = new TaskHandlers(services);
    this.birthdayHandlers = new BirthdayHandlers(services);
    this.eventHandlers = new EventHandlers(services);
    this.notificationHandlers = new NotificationHandlers(services);
    this.navigationHandler = new NavigationHandler(services);

    this.setupHandlers();
  }

  async handleWebhook(c: Context) {
    return this.botCore.handleWebhook(c);
  }

  async sendMessage(chatId: string, message: string, options?: any): Promise<void> {
    return this.botCore.sendMessage(chatId, message, options);
  }

  private setupHandlers() {
    const bot = this.botCore.getBot();

    bot.start(async (ctx) => {
      await this.handleStartCommand(ctx);
    });

    bot.help(async (ctx) => {
      await this.handleHelpCommand(ctx);
    });

    bot.command(COMMANDS.TASKS, async (ctx) => {
      await this.taskHandlers.handleTasksCommand(ctx);
    });

    bot.command(COMMANDS.PENDING, async (ctx) => {
      await this.taskHandlers.handlePendingTasksCommand(ctx);
    });

    bot.command(COMMANDS.COMPLETED, async (ctx) => {
      await this.taskHandlers.handleCompletedTasksCommand(ctx);
    });

    bot.command(COMMANDS.BIRTHDAY, async (ctx) => {
      await this.birthdayHandlers.handleBirthdayCommand(ctx);
    });

    bot.command(COMMANDS.BIRTHDAYS, async (ctx) => {
      await this.birthdayHandlers.handleBirthdaysCommand(ctx);
    });

    bot.command(COMMANDS.EVENTS, async (ctx) => {
      await this.eventHandlers.handleEventsCommand(ctx);
    });

    bot.command(COMMANDS.TODAY, async (ctx) => {
      await this.eventHandlers.handleTodayEventsCommand(ctx);
    });

    bot.command(COMMANDS.NOTIFICATIONS, async (ctx) => {
      await this.notificationHandlers.handleNotificationsCommand(ctx);
    });

    bot.on(message("text"), async (ctx) => {
      await this.messageHandler.handleTextMessage(ctx);
    });

    this.setupKeyboardHandlers();
    this.setupCallbackHandlers();
  }

  private async handleStartCommand(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    const services = this.botCore.getServices();
    const userRepo = services.repositoryFactory.getUserRepository();
    let user = await userRepo.findById(userId);

    if (!user) {
      user = await userRepo.create({
        id: userId,
        username: ctx.from?.username,
        firstName: ctx.from?.first_name || "Unknown",
        lastName: ctx.from?.last_name,
        createdAt: new Date().toISOString(),
        lastActive: new Date().toISOString(),
        preferences: {
          timezone: "UTC",
          notificationTime: "09:00",
          mode: "chat"
        }
      });
    }

    await ctx.reply(
      `${MESSAGES.WELCOME_PREFIX}, ${user.firstName}!\n\n` +
      `${MESSAGES.WELCOME_DESCRIPTION}\n\n` +
      `${FEATURES.TASK_MANAGEMENT.TITLE}\n` +
      `${FEATURES.TASK_MANAGEMENT.ITEMS.join("\n")}\n\n` +
      `${FEATURES.BIRTHDAY_TRACKING.TITLE}\n` +
      `${FEATURES.BIRTHDAY_TRACKING.ITEMS.join("\n")}\n\n` +
      `${FEATURES.CHRISTIAN_EVENTS.TITLE}\n` +
      `${FEATURES.CHRISTIAN_EVENTS.ITEMS.join("\n")}\n\n` +
      `${FEATURES.SMART_NOTIFICATIONS.TITLE}\n` +
      `${FEATURES.SMART_NOTIFICATIONS.ITEMS.join("\n")}\n\n` +
      `${FEATURES.SEARCH_CALENDAR.TITLE}\n` +
      `${FEATURES.SEARCH_CALENDAR.ITEMS.join("\n")}\n\n` +
      "Use the menu below to get started! 👇",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getStartMainMenuKeyboard()
      }
    );
  }

  private async handleHelpCommand(ctx: BotContext): Promise<void> {
    await ctx.reply(
      `${HELP_CONTENT.BOT_COMMANDS_TITLE}\n\n` +
      `${HELP_CONTENT.TASK_MANAGEMENT_SECTION}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.TASKS}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.PENDING}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.COMPLETED}\n\n` +
      `${HELP_CONTENT.BIRTHDAY_TRACKING_SECTION}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.BIRTHDAY}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.BIRTHDAYS}\n\n` +
      `${HELP_CONTENT.CHRISTIAN_EVENTS_SECTION}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.EVENTS}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.TODAY}\n\n` +
      `${HELP_CONTENT.NOTIFICATIONS_SECTION}\n` +
      `${HELP_CONTENT.COMMAND_DESCRIPTIONS.NOTIFICATIONS}\n\n` +
      `${HELP_CONTENT.CREATE_TASK_TIP}`,
      { parse_mode: "Markdown" }
    );
  }

  private setupKeyboardHandlers() {
    const bot = this.botCore.getBot();

    bot.hears(BUTTONS.TASKS, async (ctx) => {
      await ctx.reply(
        `${MENU_TITLES.TASK_MANAGEMENT}\n\n${MESSAGES.CHOOSE_ACTION}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getTaskMenuKeyboard()
        }
      );
    });

    bot.hears(BUTTONS.BIRTHDAYS, async (ctx) => {
      await ctx.reply(
        `${MENU_TITLES.BIRTHDAY_MANAGEMENT}\n\n${MESSAGES.CHOOSE_ACTION}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBirthdayMenuKeyboard()
        }
      );
    });

    bot.hears(BUTTONS.CHRISTIAN_EVENTS, async (ctx) => {
      await ctx.reply(
        `${MENU_TITLES.CHRISTIAN_EVENTS}\n\n${MESSAGES.CHOOSE_ACTION}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getEventsMenuKeyboard()
        }
      );
    });

    bot.hears(BUTTONS.NOTIFICATIONS, async (ctx) => {
      await this.notificationHandlers.handleNotificationsCommand(ctx);
    });

    bot.hears(BUTTONS.SEARCH, async (ctx) => {
      await ctx.reply(
        "🔍 *Search*\n\nChoose what to search:",
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getSearchMenuKeyboard()
        }
      );
    });

    bot.hears(BUTTONS.HELP, async (ctx) => {
      await this.handleHelpCommand(ctx);
    });
  }

  private setupCallbackHandlers() {
    const bot = this.botCore.getBot();

    bot.on("callback_query", async (ctx) => {
      const action = (ctx.callbackQuery as any)?.data;
      if (action) {
        await this.navigationHandler.handleCallback(ctx, action);
      }
    });
  }
}
