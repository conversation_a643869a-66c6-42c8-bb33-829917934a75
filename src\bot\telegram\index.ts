// Production-scale Telegram bot with advanced Telegraf patterns

import { Context } from "hono";
import { Scenes } from "telegraf";
import { BotCore, BotServices } from "./core/core";
import { TBotContext } from "./core/types";
import { RBACComposers } from "./access-control/rbac-composers";
import { TaskCreationWizard } from "./scenes/task-creation-wizard";
import { BirthdayCreationWizard } from "./scenes/birthday-creation-wizard";
import { SettingsWizard } from "./scenes/settings-wizard";
import { SubscriptionWizard } from "./scenes/subscription-wizard";
import { SearchWizard } from "./scenes/search-wizard";
import { message } from "telegraf/filters";
import { SCENES } from "./constants";

export class TelegramBot {
  private botCore: BotCore;
  private services: BotServices;
  private rbacComposers: RBACComposers;
  private taskCreationWizard: TaskCreationWizard;
  private birthdayCreationWizard: BirthdayCreationWizard;
  private settingsWizard: SettingsWizard;
  private subscriptionWizard: SubscriptionWizard;
  private searchWizard: SearchWizard;

  constructor(env: Env) {
    const botToken = env.TELEGRAM_BOT_TOKEN || env.BOT_TOKEN;

    // Initialize production bot core
    this.botCore = new BotCore(botToken, env);
    this.services = this.botCore.getServices();

    // Initialize RBAC composers
    this.rbacComposers = new RBACComposers(this.services);

    // Initialize wizards
    this.taskCreationWizard = new TaskCreationWizard(this.services);
    this.birthdayCreationWizard = new BirthdayCreationWizard(this.services);
    this.settingsWizard = new SettingsWizard(this.services);
    this.subscriptionWizard = new SubscriptionWizard(this.services);
    this.searchWizard = new SearchWizard(this.services);

    this.setupBot();
  }

  /**
   * Setup the production bot with all features
   */
  private setupBot(): void {
    const bot = this.botCore.getBot();

    // Setup scenes
    this.setupScenes();

    // Setup role-based access control
    this.setupRBAC();

    // Setup global handlers
    this.setupGlobalHandlers();

    // Setup callback handlers
    this.setupCallbackHandlers();

    // Setup error recovery
    this.setupErrorRecovery();
  }

  /**
   * Setup wizard scenes
   */
  private setupScenes(): void {
    // Add all wizard scenes
    const taskWizardScene = this.taskCreationWizard.createScene();
    const birthdayWizardScene = this.birthdayCreationWizard.createScene();
    const settingsWizardScene = this.settingsWizard.createScene();
    const subscriptionWizardScene = this.subscriptionWizard.createScene();
    const searchWizardScene = this.searchWizard.createScene();

    this.botCore.addScene(taskWizardScene);
    this.botCore.addScene(birthdayWizardScene);
    this.botCore.addScene(settingsWizardScene);
    this.botCore.addScene(subscriptionWizardScene);
    this.botCore.addScene(searchWizardScene);
  }

  /**
   * Setup role-based access control
   */
  private setupRBAC(): void {
    const bot = this.botCore.getBot();
    const { adminComposer, paidComposer, freeComposer } = this.botCore.createRoleBasedComposers();
    const rbacComposers = this.rbacComposers;

    // Setup admin features
    const adminFeatures = rbacComposers.createAdminComposer();
    bot.use(adminFeatures);

    // Setup premium features
    const premiumFeatures = rbacComposers.createPaidComposer();
    bot.use(premiumFeatures);

    // Setup AI features with quota enforcement
    const aiFeatures = rbacComposers.createAIComposer();
    bot.use(aiFeatures);

    // Setup basic features (available to all users)
    const basicFeatures = rbacComposers.createFreeComposer();
    bot.use(basicFeatures);
  }

  /**
   * Setup global handlers that work across all user tiers
   */
  private setupGlobalHandlers(): void {
    const bot = this.botCore.getBot();

    // Global commands that bypass RBAC (essential commands)
    bot.start(async (ctx) => {
      await this.services.sessionManager.updateAnalytics(ctx, "start_command");
      // The actual handler is in RBACComposers.handleStart
    });

    bot.help(async (ctx) => {
      await this.services.sessionManager.updateAnalytics(ctx, "help_command");
      // The actual handler is in RBACComposers.handleHelp
    });

    // Handle text messages with intelligent routing
    bot.on(message("text"), async (ctx) => {
      await this.handleTextMessage(ctx);
    });

    // Handle photos, documents, and other media
    bot.on(message("photo"), async (ctx) => {
      await this.handleMediaMessage(ctx, "photo");
    });

    bot.on(message("document"), async (ctx) => {
      await this.handleMediaMessage(ctx, "document");
    });

    bot.on(message("voice"), async (ctx) => {
      await this.handleMediaMessage(ctx, "voice");
    });

    // Handle location sharing
    bot.on(message("location"), async (ctx) => {
      await this.handleLocationMessage(ctx);
    });

    // Handle contact sharing
    bot.on(message("contact"), async (ctx) => {
      await this.handleContactMessage(ctx);
    });
  }

  /**
   * Setup callback query handlers
   */
  private setupCallbackHandlers(): void {
    const bot = this.botCore.getBot();

    // Main navigation callbacks
    bot.action("create_task", async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.TASK_CREATION);
    });

    bot.action("add_birthday", async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.BIRTHDAY_CREATION);
    });

    bot.action("view_tasks", async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleViewTasks(ctx);
    });

    bot.action("settings", async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.SETTINGS);
    });

    bot.action("upgrade", async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.SUBSCRIPTION);
    });

    // Search functionality
    bot.action("search", async (ctx) => {
      await ctx.answerCbQuery();
      await ctx.scene.enter(SCENES.SEARCH);
    });

    bot.command("search", async (ctx) => {
      await ctx.scene.enter(SCENES.SEARCH);
    });

    // Subscription callbacks
    bot.action("subscribe_premium", async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleSubscription(ctx, "premium");
    });

    bot.action("compare_plans", async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleComparePlans(ctx);
    });

    bot.action("upgrade_faq", async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleUpgradeFAQ(ctx);
    });

    // Settings callbacks
    bot.action(/^settings_/, async (ctx) => {
      await ctx.answerCbQuery();
      await this.handleSettingsCallback(ctx);
    });
  }

  /**
   * Setup error recovery mechanisms
   */
  private setupErrorRecovery(): void {
    const bot = this.botCore.getBot();

    // Handle unknown commands gracefully
    bot.on("message", async (ctx, next) => {
      try {
        await next();
      } catch (error) {
        console.error("Message handling error:", error);
        await ctx.reply("❌ Something went wrong. Please try again or use /help for assistance.");
      }
    });

    // Handle callback query errors
    bot.on("callback_query", async (ctx, next) => {
      try {
        await next();
      } catch (error) {
        console.error("Callback handling error:", error);
        await ctx.answerCbQuery("❌ Action failed. Please try again.");
      }
    });
  }

  /**
   * Handle text messages with intelligent routing
   */
  private async handleTextMessage(ctx: TBotContext): Promise<void> {
    if (!ctx.message || !("text" in ctx.message)) {
      return;
    }

    const text = ctx.message.text;
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    // Update analytics
    await this.services.sessionManager.updateAnalytics(ctx, "text_message");

    // Check if user is in a scene (wizard flow)
    if (ctx.scene.current) {
      // Let the scene handle the message
      return;
    }

    // Check for quick task creation (natural language)
    if (this.isTaskCreationIntent(text)) {
      await this.handleQuickTaskCreation(ctx, text);
      return;
    }

    // Check for birthday mentions
    if (this.isBirthdayIntent(text)) {
      await this.handleQuickBirthdayCreation(ctx, text);
      return;
    }

    // Check for search intent
    if (this.isSearchIntent(text)) {
      await this.handleSearch(ctx, text);
      return;
    }

    // Default response for unrecognized text
    await ctx.reply(
      "🤔 I'm not sure what you mean. Try using /help to see available commands, or use the menu buttons below.",
      {
        reply_markup: {
          inline_keyboard: [
            [
              { text: "📝 Create Task", callback_data: "create_task" },
              { text: "🎂 Add Birthday", callback_data: "add_birthday" }
            ],
            [
              { text: "📋 View Tasks", callback_data: "view_tasks" },
              { text: "⚙️ Settings", callback_data: "settings" }
            ]
          ]
        }
      }
    );
  }

  /**
   * Handle media messages (photos, documents, voice)
   */
  private async handleMediaMessage(ctx: TBotContext, mediaType: string): Promise<void> {
    await this.services.sessionManager.updateAnalytics(ctx, `${mediaType}_message`);

    // Check if user has premium features for media processing
    const hasPremiumFeatures = this.services.sessionManager.hasFeatureAccess(ctx, "enhanced_ai");

    if (hasPremiumFeatures) {
      await ctx.reply(`📎 ${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} received! Premium media processing coming soon...`);
    } else {
      await ctx.reply(
        `📎 ${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} received! Upgrade to Premium for advanced media processing.`,
        {
          reply_markup: {
            inline_keyboard: [
              [{ text: "💎 Upgrade", callback_data: "upgrade" }]
            ]
          }
        }
      );
    }
  }

  /**
   * Handle location messages
   */
  private async handleLocationMessage(ctx: TBotContext): Promise<void> {
    await this.services.sessionManager.updateAnalytics(ctx, "location_message");
    await ctx.reply("📍 Location received! Location-based features coming soon...");
  }

  /**
   * Handle contact messages
   */
  private async handleContactMessage(ctx: TBotContext): Promise<void> {
    await this.services.sessionManager.updateAnalytics(ctx, "contact_message");
    await ctx.reply("👤 Contact received! Contact management features coming soon...");
  }

  /**
   * Handle quick task creation from natural language
   */
  private async handleQuickTaskCreation(ctx: TBotContext, text: string): Promise<void> {
    // Check quota
    const canCreateTask = await this.services.sessionManager.checkQuota(ctx, "tasks");
    if (!canCreateTask) {
      await ctx.reply("📊 You've reached your task limit. Upgrade to create more tasks!");
      return;
    }

    try {
      // Use AI to extract task information if available
      const hasAI = this.services.sessionManager.hasFeatureAccess(ctx, "basic_ai");
      const canUseAI = hasAI && await this.services.sessionManager.checkQuota(ctx, "aiRequests");

      const task = await this.services.taskService.createTask(
        ctx.from!.id.toString(),
        text,
        undefined,
        "medium",
        undefined
      );

      // Consume quotas
      await this.services.sessionManager.consumeQuota(ctx, "tasks");
      if (canUseAI) {
        await this.services.sessionManager.consumeQuota(ctx, "aiRequests");
      }

      await ctx.reply(
        `✅ Task created: "${task.title}"\n\nUse /tasks to view all your tasks.`,
        {
          reply_markup: {
            inline_keyboard: [
              [{ text: "📋 View Tasks", callback_data: "view_tasks" }]
            ]
          }
        }
      );
    } catch (error) {
      console.error("Quick task creation error:", error);
      await ctx.reply("❌ Failed to create task. Please try again or use the /create command.");
    }
  }

  /**
   * Handle quick birthday creation
   */
  private async handleQuickBirthdayCreation(ctx: TBotContext, _text: string): Promise<void> {
    await ctx.reply("🎂 Quick birthday creation detected! Use /birthdays to add birthdays properly.");
  }

  /**
   * Handle search functionality
   */
  private async handleSearch(ctx: TBotContext, text: string): Promise<void> {
    const query = text.replace(/^(search|find|look for)\s+/i, "");
    await ctx.reply(`🔍 Searching for "${query}"... Search functionality coming soon!`);
  }

  /**
   * Handle view tasks
   */
  private async handleViewTasks(ctx: TBotContext): Promise<void> {
    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      const tasks = await this.services.taskService.getUserTasks(userId, "all", 5);

      if (tasks.length === 0) {
        await ctx.reply(
          "📝 No tasks yet! Create your first task to get started.",
          {
            reply_markup: {
              inline_keyboard: [
                [{ text: "➕ Create Task", callback_data: "create_task" }]
              ]
            }
          }
        );
        return;
      }

      const taskList = tasks.map((task, index) => {
        const statusIcon = task.status === "completed" ? "✅" :
          task.status === "in-progress" ? "🔄" : "⏳";
        const priorityIcon = task.priority === "high" ? "🔴" :
          task.priority === "medium" ? "🟡" : "🟢";

        return `${index + 1}. ${statusIcon} ${priorityIcon} ${task.title}`;
      }).join("\n");

      await ctx.reply(
        `📋 *Your Tasks*\n\n${taskList}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [
              [
                { text: "➕ Create Task", callback_data: "create_task" },
                { text: "📊 View All", callback_data: "view_all_tasks" }
              ]
            ]
          }
        }
      );
    } catch (error) {
      console.error("View tasks error:", error);
      await ctx.reply("❌ Failed to load tasks. Please try again.");
    }
  }

  /**
   * Handle subscription process
   */
  private async handleSubscription(ctx: TBotContext, _plan: string): Promise<void> {
    await ctx.scene.enter("subscription-wizard");
  }

  /**
   * Handle plan comparison
   */
  private async handleComparePlans(ctx: TBotContext): Promise<void> {
    const comparison = `
📊 *Plan Comparison*

**FREE**
• 10 tasks
• 5 birthdays  
• 3 AI requests/day
• Basic notifications
• Standard support

**PREMIUM** ($9.99/month)
• Unlimited tasks
• Unlimited birthdays
• 100 AI requests/day
• Advanced notifications
• Data export
• Task templates
• Priority support
• Custom themes

**ADMIN**
• Everything in Premium
• User management
• System analytics
• Broadcast messages
• API access
    `;

    await ctx.reply(comparison, {
      parse_mode: "Markdown",
      reply_markup: {
        inline_keyboard: [
          [{ text: "💎 Upgrade to Premium", callback_data: "subscribe_premium" }]
        ]
      }
    });
  }

  /**
   * Handle upgrade FAQ
   */
  private async handleUpgradeFAQ(ctx: TBotContext): Promise<void> {
    const faq = `
❓ *Upgrade FAQ*

**Q: How do I upgrade?**
A: Click "Subscribe Now" and follow the payment process.

**Q: Can I cancel anytime?**
A: Yes, cancel anytime. No long-term commitments.

**Q: What payment methods do you accept?**
A: Credit cards, PayPal, and cryptocurrency.

**Q: Do I keep my data if I downgrade?**
A: Yes, but some features may be limited.

**Q: Is there a free trial?**
A: New users get 7 days of Premium features free!

Need more help? Contact support!
    `;

    await ctx.reply(faq, { parse_mode: "Markdown" });
  }

  /**
   * Handle settings callbacks
   */
  private async handleSettingsCallback(ctx: TBotContext): Promise<void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const setting = ctx.callbackQuery.data.replace("settings_", "");
    await ctx.reply(`⚙️ ${setting.charAt(0).toUpperCase() + setting.slice(1)} settings coming soon...`);
  }

  // Intent detection helpers
  private isTaskCreationIntent(text: string): boolean {
    const taskKeywords = ["todo", "task", "remind me", "need to", "have to", "must", "should"];
    const lowerText = text.toLowerCase();
    return taskKeywords.some(keyword => lowerText.includes(keyword)) && text.length > 10;
  }

  private isBirthdayIntent(text: string): boolean {
    const birthdayKeywords = ["birthday", "born", "birth date", "bday"];
    const lowerText = text.toLowerCase();
    return birthdayKeywords.some(keyword => lowerText.includes(keyword));
  }

  private isSearchIntent(text: string): boolean {
    const searchKeywords = ["search", "find", "look for", "where is"];
    const lowerText = text.toLowerCase();
    return searchKeywords.some(keyword => lowerText.startsWith(keyword));
  }

  /**
   * Handle webhook requests
   */
  async handleWebhook(c: Context): Promise<Response> {
    return this.botCore.handleWebhook(c);
  }

  /**
   * Get bot services for external access
   */
  getServices(): BotServices {
    return this.services;
  }
}
