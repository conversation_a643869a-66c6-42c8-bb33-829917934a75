import { BotContext, BotServices } from "../core/core";
import { SessionManager } from "../core/session-manager";
import { MenuKeyboards } from "../ui/menu-keyboards";
import { Markup } from "telegraf";
import {
  MENU_TITLES,
  MESSAGES,
  CA<PERSON><PERSON><PERSON>KS,
  CALLBACK_PREFIXES,
  BUTTONS
} from "../../constants";

export class BirthdayHandlers {
  constructor(private services: BotServices) { }

  // Command handlers
  async handleBirthdayCommand(ctx: BotContext): Promise<void> {
    await ctx.reply(
      `${MENU_TITLES.BIRTHDAY_MANAGEMENT}\n\n${MESSAGES.CHOOSE_ACTION}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBirthdayMenuKeyboard()
      }
    );
  }

  async handleBirthdaysCommand(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const upcomingBirthdays = await this.services.birthdayService.getUpcomingBirthdays(userId, 30);

      if (upcomingBirthdays.length === 0) {
        await ctx.reply(MESSAGES.NO_UPCOMING_BIRTHDAYS);
        return;
      }

      const birthdayList = upcomingBirthdays.map((birthday, index) => {
        const nextBirthday = this.services.birthdayService.getNextBirthdayDate(birthday);
        const daysUntil = Math.ceil((nextBirthday.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        const daysText = daysUntil === 1 ? MESSAGES.DAY_SUFFIX : MESSAGES.DAYS_SUFFIX;
        return `${index + 1}. 🎂 *${birthday.name}* - ${nextBirthday.toLocaleDateString()} (${daysUntil} ${daysText})`;
      }).join("\n");

      await ctx.reply(`${MESSAGES.UPCOMING_BIRTHDAYS}\n\n${birthdayList}`, { parse_mode: "Markdown" });
    } catch (error) {
      console.error("Error fetching upcoming birthdays:", error);
      await ctx.reply(MESSAGES.ERROR_LOADING_BIRTHDAYS);
    }
  }

  // Callback handlers
  async handleBirthdaysMenu(ctx: BotContext): Promise<void> {
    SessionManager.setLastAction(ctx, CALLBACKS.BIRTHDAYS_MENU);

    await ctx.editMessageText(
      `${MENU_TITLES.BIRTHDAY_MANAGEMENT}\n\n${MESSAGES.CHOOSE_ACTION}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBirthdayMenuKeyboard()
      }
    );
  }

  async handleBirthdayAdd(ctx: BotContext): Promise<void> {
    SessionManager.startBirthdayCreation(ctx);

    await ctx.editMessageText(
      `${MENU_TITLES.ADD_NEW_BIRTHDAY}\n\n${MESSAGES.ENTER_PERSON_NAME}`,
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getCancelKeyboard()
      }
    );
  }

  async handleBirthdayList(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const birthdays = await this.services.birthdayService.getUserBirthdays(userId);

      if (birthdays.length === 0) {
        await ctx.editMessageText(
          `${MESSAGES.YOUR_BIRTHDAYS}\n\n${MESSAGES.NO_BIRTHDAYS_FOUND}`,
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAYS_MENU)
          }
        );
        return;
      }

      const birthdayButtons = birthdays.map((birthday) => {
        const dateStr = birthday.year ?
          `${birthday.date.day}/${birthday.date.month}/${birthday.year}` :
          `${birthday.date.day}/${birthday.date.month}`;

        return [Markup.button.callback(
          `🎂 ${birthday.name} - ${dateStr}`,
          `${CALLBACK_PREFIXES.BIRTHDAY_DETAILS}${birthday.id}`
        )];
      });

      birthdayButtons.push([Markup.button.callback("🔙 Back", CALLBACKS.BIRTHDAYS_MENU)]);

      await ctx.editMessageText(
        `${MESSAGES.YOUR_BIRTHDAYS}\n\n${MESSAGES.SELECT_BIRTHDAY_TO_VIEW}`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: birthdayButtons
          }
        }
      );
    } catch (error) {
      console.error("Error fetching birthdays:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_LOADING_BIRTHDAYS,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAYS_MENU)
        }
      );
    }
  }

  async handleBirthdayUpcoming(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const upcomingBirthdays = await this.services.birthdayService.getUpcomingBirthdays(userId, 30);

      if (upcomingBirthdays.length === 0) {
        await ctx.editMessageText(
          "🎉 *Upcoming Birthdays*\n\n" +
          "No birthdays in the next 30 days.",
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAYS_MENU)
          }
        );
        return;
      }

      const birthdayList = upcomingBirthdays.map((birthday, index) => {
        const nextBirthday = this.services.birthdayService.getNextBirthdayDate(birthday);
        const daysUntil = Math.ceil((nextBirthday.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        return `${index + 1}. 🎂 *${birthday.name}* - ${nextBirthday.toLocaleDateString()} (${daysUntil} days)`;
      }).join("\n");

      await ctx.editMessageText(
        `🎉 *Upcoming Birthdays*\n\n${birthdayList}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAYS_MENU)
        }
      );
    } catch (error) {
      console.error("Error fetching upcoming birthdays:", error);
      await ctx.editMessageText(
        "❌ Error loading upcoming birthdays. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAYS_MENU)
        }
      );
    }
  }

  async handleBirthdayDetails(ctx: BotContext, birthdayId: string): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const birthday = await this.services.birthdayService.getBirthday(birthdayId, userId);

      if (!birthday) {
        await ctx.editMessageText(
          "❌ Birthday not found.",
          {
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAY_LIST)
          }
        );
        return;
      }

      const dateStr = birthday.year ?
        `${birthday.date.day}/${birthday.date.month}/${birthday.year}` :
        `${birthday.date.day}/${birthday.date.month}`;

      const nextBirthday = this.services.birthdayService.getNextBirthdayDate(birthday);
      const daysUntil = this.services.birthdayService.getDaysUntilBirthday(birthday);

      let age = "";
      if (birthday.year) {
        const currentAge = new Date().getFullYear() - birthday.year;
        const nextAge = nextBirthday.getFullYear() - birthday.year;
        age = `*Current Age:* ${currentAge}\n*Next Age:* ${nextAge}\n`;
      }

      const message = "🎂 *Birthday Details*\n\n" +
        `*Name:* ${birthday.name}\n` +
        `*Date:* ${dateStr}\n` +
        age +
        `*Next Birthday:* ${nextBirthday.toLocaleDateString()}\n` +
        `*Days Until:* ${daysUntil} days\n` +
        `*Reminders:* ${birthday.reminderDays.join(", ")} days before\n` +
        `*Status:* ${birthday.isActive ? "Active" : "Inactive"}`;

      await ctx.editMessageText(message, {
        parse_mode: "Markdown",
        ...MenuKeyboards.getBirthdayActionKeyboard(birthdayId)
      });
    } catch (error) {
      console.error("Error fetching birthday details:", error);
      await ctx.editMessageText(
        "❌ Error loading birthday details. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAY_LIST)
        }
      );
    }
  }

  async handleBirthdayDelete(ctx: BotContext, birthdayId: string): Promise<void> {
    await ctx.editMessageText(
      "🗑️ *Delete Birthday*\n\nAre you sure you want to delete this birthday? This action cannot be undone.",
      {
        parse_mode: "Markdown",
        ...MenuKeyboards.getConfirmationKeyboard(`${CALLBACK_PREFIXES.CONFIRM_DELETE_BIRTHDAY}${birthdayId}`, CALLBACKS.BIRTHDAY_LIST)
      }
    );
  }

  async handleBirthdayDeleteConfirm(ctx: BotContext, birthdayId: string): Promise<void> {
    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      // Get birthday details before deletion for confirmation message
      const birthday = await this.services.birthdayService.getBirthday(birthdayId, userId);

      if (!birthday) {
        await ctx.editMessageText(
          "❌ Birthday not found.",
          {
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAY_LIST)
          }
        );
        return;
      }

      // Delete the birthday
      await this.services.birthdayService.deleteBirthday(birthdayId, userId);

      await ctx.editMessageText(
        `🗑️ *Birthday Deleted*\n\nBirthday for "${birthday.name}" has been permanently deleted.`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [[
              { text: BUTTONS.VIEW_BIRTHDAYS, callback_data: CALLBACKS.BIRTHDAY_LIST },
              { text: BUTTONS.MAIN_MENU, callback_data: CALLBACKS.MAIN_MENU }
            ]]
          }
        }
      );
    } catch (error) {
      console.error("Error deleting birthday:", error);
      await ctx.editMessageText(
        "❌ Error deleting birthday. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.BIRTHDAY_LIST)
        }
      );
    }
  }
}
