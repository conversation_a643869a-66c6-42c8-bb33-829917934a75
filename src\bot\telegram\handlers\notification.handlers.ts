import { Bot<PERSON>ontext, BotServices } from "../core/core";
import { SessionManager } from "../core/session-manager";
import { MenuKeyboards } from "../ui/menu-keyboards";
import {
  MENU_TITLES,
  MESSAGES,
  CALLBACKS,
  BUTTONS
} from "../../constants";

export class NotificationHandlers {
  constructor(private services: BotServices) { }

  // Command handlers
  async handleNotificationsCommand(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      // Get notification statistics
      const notifications = await this.services.notificationService.getUserNotifications(userId, undefined, 5);
      const pendingCount = notifications.filter(n => n.status === "pending").length;
      const recentCount = notifications.filter(n => n.status === "sent").length;

      await ctx.reply(
        `${MENU_TITLES.NOTIFICATION_CENTER}\n\n` +
        `${MESSAGES.STATISTICS}\n` +
        `⏰ ${MESSAGES.PENDING} ${pendingCount}\n` +
        `✅ ${MESSAGES.RECENT} ${recentCount}\n` +
        `📋 ${MESSAGES.TOTAL} ${notifications.length}\n\n` +
        `${MESSAGES.CHOOSE_ACTION}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getNotificationsMenuKeyboard()
        }
      );
    } catch (error) {
      console.error("Error fetching notification stats:", error);
      await ctx.reply("❌ Error loading notifications. Please try again.");
    }
  }

  // Callback handlers
  async handleNotificationsMenu(ctx: BotContext): Promise<void> {
    SessionManager.setLastAction(ctx, CALLBACKS.NOTIFICATIONS_MENU);

    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      // Get notification statistics
      const notifications = await this.services.notificationService.getUserNotifications(userId, undefined, 10);
      const pendingCount = notifications.filter(n => n.status === "pending").length;
      const recentCount = notifications.filter(n => n.status === "sent").length;

      await ctx.editMessageText(
        "🔔 *Notification Center*\n\n" +
        "📊 *Statistics:*\n" +
        `⏰ Pending: ${pendingCount}\n` +
        `✅ Recent: ${recentCount}\n` +
        `📋 Total: ${notifications.length}\n\n` +
        "Choose an action below:",
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getNotificationsMenuKeyboard()
        }
      );
    } catch (error) {
      console.error("Error fetching notification stats:", error);
      await ctx.editMessageText(
        "❌ Error loading notifications. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard("main_menu")
        }
      );
    }
  }

  async handleNotificationsList(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const notifications = await this.services.notificationService.getUserNotifications(userId, undefined, 10);

      if (notifications.length === 0) {
        await ctx.editMessageText(
          "🔔 *Your Notifications*\n\n" +
          "No notifications found.",
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
          }
        );
        return;
      }

      const notificationList = notifications.map((notification, index) => {
        const statusIcon = notification.status === "pending" ? "⏰" :
          notification.status === "sent" ? "✅" : "❌";
        const typeIcon = notification.type === "task-reminder" ? "📝" :
          notification.type === "birthday" ? "🎂" : "✝️";

        const scheduledDate = new Date(notification.scheduledFor).toLocaleDateString();

        return `${index + 1}. ${statusIcon} ${typeIcon} ${notification.message}\n   📅 ${scheduledDate}`;
      }).join("\n\n");

      await ctx.editMessageText(
        `🔔 *Your Notifications*\n\n${notificationList}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    } catch (error) {
      console.error("Error fetching notifications:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_LOADING_NOTIFICATIONS,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    }
  }

  async handleNotificationsPending(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const notifications = await this.services.notificationService.getUserNotifications(userId, "pending", 10);

      if (notifications.length === 0) {
        await ctx.editMessageText(
          "⏰ *Pending Notifications*\n\n" +
          "No pending notifications.",
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
          }
        );
        return;
      }

      const notificationList = notifications.map((notification, index) => {
        const typeIcon = notification.type === "task-reminder" ? "📝" :
          notification.type === "birthday" ? "🎂" : "✝️";

        const scheduledDate = new Date(notification.scheduledFor).toLocaleDateString();
        const scheduledTime = new Date(notification.scheduledFor).toLocaleTimeString();

        return `${index + 1}. ${typeIcon} ${notification.message}\n   📅 ${scheduledDate} at ${scheduledTime}`;
      }).join("\n\n");

      await ctx.editMessageText(
        `⏰ *Pending Notifications*\n\n${notificationList}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    } catch (error) {
      console.error("Error fetching pending notifications:", error);
      await ctx.editMessageText(
        MESSAGES.ERROR_LOADING_NOTIFICATIONS,
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    }
  }

  async handleNotificationsRecent(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      const notifications = await this.services.notificationService.getUserNotifications(userId, "sent", 10);

      if (notifications.length === 0) {
        await ctx.editMessageText(
          "✅ *Recent Notifications*\n\n" +
          "No recent notifications.",
          {
            parse_mode: "Markdown",
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
          }
        );
        return;
      }

      const notificationList = notifications.map((notification, index) => {
        const typeIcon = notification.type === "task-reminder" ? "📝" :
          notification.type === "birthday" ? "🎂" : "✝️";

        const sentDate = new Date(notification.sentAt || notification.scheduledFor).toLocaleDateString();

        return `${index + 1}. ${typeIcon} ${notification.message}\n   📅 Sent: ${sentDate}`;
      }).join("\n\n");

      await ctx.editMessageText(
        `✅ *Recent Notifications*\n\n${notificationList}`,
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    } catch (error) {
      console.error("Error fetching recent notifications:", error);
      await ctx.editMessageText(
        "❌ Error loading recent notifications. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    }
  }

  async handleNotificationsSettings(ctx: BotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    try {
      // Get user preferences
      const userRepo = this.services.repositoryFactory.getUserRepository();
      const user = await userRepo.findById(userId);

      if (!user) {
        await ctx.editMessageText(
          "❌ User not found.",
          {
            ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
          }
        );
        return;
      }

      const settings = user.preferences || {
        timezone: "UTC",
        notificationTime: "09:00",
        mode: "chat"
      };

      await ctx.editMessageText(
        "⚙️ *Notification Settings*\n\n" +
        `🌍 *Timezone:* ${settings.timezone}\n` +
        `⏰ *Default Time:* ${settings.notificationTime}\n` +
        `💬 *Mode:* ${settings.mode}\n\n` +
        "Settings management is coming soon!",
        {
          parse_mode: "Markdown",
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    } catch (error) {
      console.error("Error fetching notification settings:", error);
      await ctx.editMessageText(
        "❌ Error loading settings. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    }
  }

  async handleNotificationCancel(ctx: BotContext, notificationId: string): Promise<void> {
    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      await this.services.notificationService.cancelNotification(notificationId);

      await ctx.editMessageText(
        "✅ *Notification Cancelled*\n\nThe notification has been cancelled successfully.",
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [[
              { text: BUTTONS.VIEW_NOTIFICATIONS, callback_data: CALLBACKS.NOTIFICATIONS_LIST },
              { text: BUTTONS.MAIN_MENU, callback_data: CALLBACKS.MAIN_MENU }
            ]]
          }
        }
      );
    } catch (error) {
      console.error("Error cancelling notification:", error);
      await ctx.editMessageText(
        "❌ Error cancelling notification. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    }
  }

  async handleNotificationSnooze(ctx: BotContext, notificationId: string, minutes: number = 30): Promise<void> {
    try {
      const userId = ctx.from?.id.toString();
      if (!userId) {
        return;
      }

      // Snooze by updating the scheduled time
      const newScheduledTime = new Date();
      newScheduledTime.setMinutes(newScheduledTime.getMinutes() + minutes);

      await this.services.notificationService.updateNotification(notificationId, userId, {
        scheduledFor: newScheduledTime.toISOString(),
        status: "snoozed"
      });

      await ctx.editMessageText(
        `⏰ *Notification Snoozed*\n\nThe notification has been snoozed for ${minutes} minutes.`,
        {
          parse_mode: "Markdown",
          reply_markup: {
            inline_keyboard: [[
              { text: BUTTONS.VIEW_NOTIFICATIONS, callback_data: CALLBACKS.NOTIFICATIONS_LIST },
              { text: BUTTONS.MAIN_MENU, callback_data: CALLBACKS.MAIN_MENU }
            ]]
          }
        }
      );
    } catch (error) {
      console.error("Error snoozing notification:", error);
      await ctx.editMessageText(
        "❌ Error snoozing notification. Please try again.",
        {
          ...MenuKeyboards.getBackKeyboard(CALLBACKS.NOTIFICATIONS_MENU)
        }
      );
    }
  }
}
