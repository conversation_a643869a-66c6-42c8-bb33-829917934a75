import { CALLBACKS } from "@/bot/constants";
import { BotContext } from "./core";

export class SessionManager {

  /**
   * Initialize session if it doesn't exist
   */
  static initializeSession(ctx: BotContext): void {
    if (!ctx.session) {
      ctx.session = {};
    }
  }

  /**
   * Clear all session data
   */
  static clearSession(ctx: BotContext): void {
    if (ctx.session) {
      ctx.session = {};
    }
  }

  /**
   * Clear specific session data for tasks
   */
  static clearTaskSession(ctx: BotContext): void {
    if (ctx.session) {
      ctx.session.awaitingTaskTitle = undefined;
      ctx.session.awaitingTaskDescription = undefined;
      ctx.session.tempTaskData = undefined;
      ctx.session.editingTaskId = undefined;
    }
  }

  /**
   * Clear specific session data for birthdays
   */
  static clearBirthdaySession(ctx: BotContext): void {
    if (ctx.session) {
      ctx.session.awaitingBirthdayName = undefined;
      ctx.session.awaitingBirthdayDate = undefined;
      ctx.session.tempBirthdayData = undefined;
      ctx.session.editingBirthdayId = undefined;
    }
  }

  /**
   * Clear specific session data for events
   */
  static clearEventSession(ctx: BotContext): void {
    if (ctx.session) {
      ctx.session.awaitingEventName = undefined;
      ctx.session.tempEventData = undefined;
    }
  }

  /**
   * Clear integration session data
   */
  static clearIntegrationSession(ctx: BotContext): void {
    if (ctx.session) {
      ctx.session.awaitingInput = undefined;
      ctx.session.searchMode = undefined;
    }
  }

  /**
   * Check if user is in any input-awaiting state
   */
  static isAwaitingInput(ctx: BotContext): boolean {
    if (!ctx.session) {
      return false;
    }

    return !!(
      ctx.session.awaitingTaskTitle ||
      ctx.session.awaitingTaskDescription ||
      ctx.session.awaitingBirthdayName ||
      ctx.session.awaitingBirthdayDate ||
      ctx.session.awaitingEventName ||
      ctx.session.awaitingInput
    );
  }

  /**
   * Check if user is in task creation flow
   */
  static isInTaskFlow(ctx: BotContext): boolean {
    if (!ctx.session) {
      return false;
    }

    return !!(
      ctx.session.awaitingTaskTitle ||
      ctx.session.awaitingTaskDescription ||
      ctx.session.tempTaskData
    );
  }

  /**
   * Check if user is in birthday creation flow
   */
  static isInBirthdayFlow(ctx: BotContext): boolean {
    if (!ctx.session) {
      return false;
    }

    return !!(
      ctx.session.awaitingBirthdayName ||
      ctx.session.awaitingBirthdayDate ||
      ctx.session.tempBirthdayData
    );
  }

  /**
   * Check if user is in event creation flow
   */
  static isInEventFlow(ctx: BotContext): boolean {
    if (!ctx.session) {
      return false;
    }

    return !!(
      ctx.session.awaitingEventName ||
      ctx.session.tempEventData
    );
  }

  /**
   * Set last action for navigation tracking
   */
  static setLastAction(ctx: BotContext, action: string): void {
    if (ctx.session) {
      ctx.session.lastAction = action;
    }
  }

  /**
   * Get last action for navigation
   */
  static getLastAction(ctx: BotContext): string | undefined {
    return ctx.session?.lastAction;
  }

  /**
   * Start task creation flow
   */
  static startTaskCreation(ctx: BotContext): void {
    if (ctx.session) {
      this.clearTaskSession(ctx);
      ctx.session.tempTaskData = {};
      ctx.session.awaitingTaskTitle = true;
      this.setLastAction(ctx, CALLBACKS.TASK_CREATE);
    }
  }

  /**
   * Start birthday creation flow
   */
  static startBirthdayCreation(ctx: BotContext): void {
    if (ctx.session) {
      this.clearBirthdaySession(ctx);
      ctx.session.tempBirthdayData = {};
      ctx.session.awaitingBirthdayName = true;
      this.setLastAction(ctx, CALLBACKS.BIRTHDAY_ADD);
    }
  }

  /**
   * Start event creation flow
   */
  static startEventCreation(ctx: BotContext): void {
    if (ctx.session) {
      this.clearEventSession(ctx);
      ctx.session.tempEventData = {};
      ctx.session.awaitingEventName = true;
      this.setLastAction(ctx, CALLBACKS.EVENT_CREATE);
    }
  }

  /**
   * Cancel current operation and clear relevant session data
   */
  static cancelCurrentOperation(ctx: BotContext): string {
    if (!ctx.session) {
      return CALLBACKS.MAIN_MENU;
    }

    let returnMenu: string = CALLBACKS.MAIN_MENU;

    if (this.isInTaskFlow(ctx)) {
      this.clearTaskSession(ctx);
      returnMenu = CALLBACKS.TASKS_MENU;
    } else if (this.isInBirthdayFlow(ctx)) {
      this.clearBirthdaySession(ctx);
      returnMenu = CALLBACKS.BIRTHDAYS_MENU;
    } else if (this.isInEventFlow(ctx)) {
      this.clearEventSession(ctx);
      returnMenu = CALLBACKS.EVENTS_MENU;
    } else if (ctx.session.awaitingInput) {
      this.clearIntegrationSession(ctx);
      returnMenu = CALLBACKS.MAIN_MENU;
    }

    return returnMenu;
  }
}
